package com.example.coloringproject.utils

import android.content.Context
import android.graphics.*
import android.util.Log
import com.example.coloringproject.data.Region

/**
 * 马赛克图片管理器
 * 使用预制的blue.png图片进行高性能马赛克绘制，替代实时计算
 */
class MosaicImageManager(private val context: Context) {
    
    private val TAG = "MosaicImageManager"
    
    // 马赛克相关
    private var mosaicBitmap: Bitmap? = null
    private var mosaicShader: BitmapShader? = null
    private val mosaicPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    
    // 性能统计
    private var loadTime = 0L
    private var drawCount = 0
    
    init {
        loadMosaicImage()
    }
    
    /**
     * 从assets加载马赛克图片
     */
    private fun loadMosaicImage() {
        val startTime = System.currentTimeMillis()
        
        try {
            // 从assets加载blue.png
            val inputStream = context.assets.open("blue.png")
            mosaicBitmap = BitmapFactory.decodeStream(inputStream)
            inputStream.close()
            
            mosaicBitmap?.let { bitmap ->
                Log.d(TAG, "马赛克图片加载成功: ${bitmap.width}x${bitmap.height}")
                
                // 创建平铺着色器
                mosaicShader = BitmapShader(
                    bitmap,
                    Shader.TileMode.REPEAT,
                    Shader.TileMode.REPEAT
                )
                
                // 设置画笔
                mosaicPaint.shader = mosaicShader
                mosaicPaint.alpha = 128 // 半透明效果，与原有马赛克保持一致
                mosaicPaint.isAntiAlias = true
                
                loadTime = System.currentTimeMillis() - startTime
                Log.d(TAG, "马赛克着色器创建完成，耗时: ${loadTime}ms")
                
            } ?: run {
                Log.e(TAG, "马赛克图片解码失败")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "加载马赛克图片失败", e)
            // 创建备用的纯色画笔
            createFallbackPaint()
        }
    }
    
    /**
     * 创建备用的纯色画笔（当图片加载失败时使用）
     */
    private fun createFallbackPaint() {
        mosaicPaint.shader = null
        mosaicPaint.color = Color.parseColor("#5B9BD5") // 蓝色
        mosaicPaint.alpha = 128
        Log.w(TAG, "使用备用纯色马赛克")
    }
    
    /**
     * 绘制矩形区域的马赛克
     * 这是最高性能的绘制方式
     */
    fun drawMosaicRect(canvas: Canvas, rect: RectF) {
        if (rect.isEmpty) return
        
        try {
            canvas.drawRect(rect, mosaicPaint)
            drawCount++
        } catch (e: Exception) {
            Log.e(TAG, "绘制马赛克矩形失败", e)
        }
    }
    
    /**
     * 绘制单个区域的马赛克（使用边界框）
     * 适用于有边界框数据的区域
     */
    fun drawMosaicRegion(canvas: Canvas, region: Region) {
        val boundingBox = region.boundingBox
        if (boundingBox != null && boundingBox.size >= 4) {
            val rect = RectF(
                boundingBox[0].toFloat(),
                boundingBox[1].toFloat(),
                boundingBox[2].toFloat(),
                boundingBox[3].toFloat()
            )
            drawMosaicRect(canvas, rect)
        } else {
            // 回退到像素级绘制（性能较低，但保证覆盖）
            drawMosaicPixels(canvas, region)
        }
    }

    /**
     * 绘制单个活跃区域的马赛克（优化版本）
     * 专门用于单区域马赛克模式，提供最佳性能
     */
    fun drawActiveRegionMosaic(canvas: Canvas, region: Region) {
        if (region.boundingBox != null && region.boundingBox.size >= 4) {
            // 使用边界框绘制，性能最优
            val rect = RectF(
                region.boundingBox[0].toFloat(),
                region.boundingBox[1].toFloat(),
                region.boundingBox[2].toFloat(),
                region.boundingBox[3].toFloat()
            )
            drawMosaicRect(canvas, rect)
            drawCount++

            Log.d(TAG, "绘制活跃区域马赛克: region=${region.id}, bounds=${rect}")
        } else {
            // 回退到标准绘制
            drawMosaicRegion(canvas, region)
        }
    }
    
    /**
     * 绘制区域的像素级马赛克（备用方案）
     * 当没有边界框数据时使用
     */
    private fun drawMosaicPixels(canvas: Canvas, region: Region) {
        if (region.pixels.isEmpty()) return
        
        try {
            // 计算像素的边界框
            val minX = region.pixels.minOf { it[0] }
            val minY = region.pixels.minOf { it[1] }
            val maxX = region.pixels.maxOf { it[0] }
            val maxY = region.pixels.maxOf { it[1] }
            
            val rect = RectF(
                minX.toFloat(),
                minY.toFloat(),
                (maxX + 1).toFloat(),
                (maxY + 1).toFloat()
            )
            
            drawMosaicRect(canvas, rect)
            
        } catch (e: Exception) {
            Log.e(TAG, "绘制像素级马赛克失败", e)
        }
    }
    
    /**
     * 批量绘制多个区域的马赛克
     * 通过计算总边界框实现最高性能
     */
    fun drawMosaicRegions(canvas: Canvas, regions: List<Region>) {
        if (regions.isEmpty()) return
        
        try {
            // 计算所有区域的总边界框
            val totalBounds = calculateTotalBounds(regions)
            
            if (totalBounds != null) {
                // 方案A：直接绘制总边界框（最快，但可能有多余区域）
                if (regions.size > 10) {
                    drawMosaicRect(canvas, totalBounds)
                } else {
                    // 方案B：逐个绘制区域（精确但稍慢）
                    regions.forEach { region ->
                        drawMosaicRegion(canvas, region)
                    }
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "批量绘制马赛克失败", e)
        }
    }
    
    /**
     * 计算多个区域的总边界框
     */
    private fun calculateTotalBounds(regions: List<Region>): RectF? {
        if (regions.isEmpty()) return null
        
        var minX = Int.MAX_VALUE
        var minY = Int.MAX_VALUE
        var maxX = Int.MIN_VALUE
        var maxY = Int.MIN_VALUE
        
        regions.forEach { region ->
            val boundingBox = region.boundingBox
            if (boundingBox != null && boundingBox.size >= 4) {
                minX = minOf(minX, boundingBox[0])
                minY = minOf(minY, boundingBox[1])
                maxX = maxOf(maxX, boundingBox[2])
                maxY = maxOf(maxY, boundingBox[3])
            } else if (region.pixels.isNotEmpty()) {
                // 从像素计算边界
                val regionMinX = region.pixels.minOf { it[0] }
                val regionMinY = region.pixels.minOf { it[1] }
                val regionMaxX = region.pixels.maxOf { it[0] }
                val regionMaxY = region.pixels.maxOf { it[1] }
                
                minX = minOf(minX, regionMinX)
                minY = minOf(minY, regionMinY)
                maxX = maxOf(maxX, regionMaxX)
                maxY = maxOf(maxY, regionMaxY)
            }
        }
        
        return if (minX != Int.MAX_VALUE) {
            RectF(minX.toFloat(), minY.toFloat(), (maxX + 1).toFloat(), (maxY + 1).toFloat())
        } else {
            null
        }
    }
    
    /**
     * 设置马赛克透明度
     */
    fun setMosaicAlpha(alpha: Float) {
        val alphaInt = (255 * alpha.coerceIn(0f, 1f)).toInt()
        mosaicPaint.alpha = alphaInt
    }
    
    /**
     * 获取性能统计信息
     */
    fun getPerformanceStats(): String {
        return "马赛克管理器统计: 加载耗时=${loadTime}ms, 绘制次数=$drawCount, 图片尺寸=${mosaicBitmap?.width}x${mosaicBitmap?.height}"
    }
    
    /**
     * 释放资源
     */
    fun release() {
        try {
            mosaicBitmap?.recycle()
            mosaicBitmap = null
            mosaicShader = null
            Log.d(TAG, "马赛克资源已释放")
        } catch (e: Exception) {
            Log.e(TAG, "释放马赛克资源失败", e)
        }
    }
    
    /**
     * 检查是否已准备就绪
     */
    fun isReady(): Boolean {
        return mosaicShader != null || mosaicPaint.color != 0
    }
}