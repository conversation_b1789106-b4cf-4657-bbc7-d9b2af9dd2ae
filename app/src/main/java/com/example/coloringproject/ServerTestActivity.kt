package com.example.coloringproject

import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.coloringproject.databinding.ActivityServerTestBinding
import com.example.coloringproject.network.ResourceDownloadManager
import kotlinx.coroutines.launch
import okhttp3.OkHttpClient
import okhttp3.Request
import java.util.concurrent.TimeUnit

/**
 * 服务器连接测试Activity
 * 专门测试8083端口的服务器连接
 */
class ServerTestActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityServerTestBinding
    private lateinit var downloadManager: ResourceDownloadManager
    
    companion object {
        private const val TAG = "ServerTestActivity"
        private const val SERVER_BASE = "http://192.168.110.80:8083"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityServerTestBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        downloadManager = ResourceDownloadManager(this)
        
        setupUI()
        runServerTests()
    }
    
    private fun setupUI() {
        binding.btnTestConnection.setOnClickListener { testBasicConnection() }
        binding.btnTestCategories.setOnClickListener { testCategoriesAPI() }
        binding.btnTestProjects.setOnClickListener { testProjectsAPI() }
        binding.btnTestDownload.setOnClickListener { testDownloadAPI() }
        binding.btnTestAll.setOnClickListener { runServerTests() }
    }
    
    private fun runServerTests() {
        binding.tvResults.text = "开始服务器测试...\n\n"
        
        lifecycleScope.launch {
            // 1. 基础连接测试
            testBasicConnection()
            
            // 2. API端点测试
            testCategoriesAPI()
            testProjectsAPI()
            
            // 3. 下载功能测试
            testDownloadAPI()
        }
    }
    
    private fun testBasicConnection() {
        binding.tvResults.append("=== 基础连接测试 ===\n")
        
        lifecycleScope.launch {
            try {
                val client = OkHttpClient.Builder()
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .readTimeout(10, TimeUnit.SECONDS)
                    .build()
                
                // 测试多个端点
                val endpoints = listOf(
                    "$SERVER_BASE/",
                    "$SERVER_BASE/api/",
                    "$SERVER_BASE/api/client/",
                    "$SERVER_BASE/health",
                    "$SERVER_BASE/api/client/categories",
                    "$SERVER_BASE/api/client/projects"
                )
                
                for (endpoint in endpoints) {
                    try {
                        val request = Request.Builder().url(endpoint).build()
                        val response = client.newCall(request).execute()
                        
                        val status = if (response.isSuccessful) "✅ 成功" else "⚠️ ${response.code}"
                        binding.tvResults.append("$endpoint: $status\n")
                        Log.i(TAG, "测试端点 $endpoint: ${response.code} - ${response.message}")
                        
                        response.close()
                    } catch (e: Exception) {
                        binding.tvResults.append("$endpoint: ❌ 失败 - ${e.message}\n")
                        Log.e(TAG, "测试端点失败: $endpoint", e)
                    }
                }
                
                binding.tvResults.append("\n")
                
            } catch (e: Exception) {
                binding.tvResults.append("基础连接测试失败: ${e.message}\n\n")
                Log.e(TAG, "基础连接测试失败", e)
            }
        }
    }
    
    private fun testCategoriesAPI() {
        binding.tvResults.append("=== 分类API测试 ===\n")
        
        lifecycleScope.launch {
            try {
                val result = downloadManager.getCategoriesList()
                
                if (result.isSuccess) {
                    val response = result.getOrNull()
                    binding.tvResults.append("✅ 分类API成功\n")
                    binding.tvResults.append("响应数据: ${response?.data}\n")
                    Log.i(TAG, "分类API测试成功: $response")
                } else {
                    val error = result.exceptionOrNull()?.message ?: "未知错误"
                    binding.tvResults.append("❌ 分类API失败: $error\n")
                    Log.e(TAG, "分类API测试失败: $error")
                }
                
                binding.tvResults.append("\n")
                
            } catch (e: Exception) {
                binding.tvResults.append("❌ 分类API异常: ${e.message}\n\n")
                Log.e(TAG, "分类API测试异常", e)
            }
        }
    }
    
    private fun testProjectsAPI() {
        binding.tvResults.append("=== 项目列表API测试 ===\n")
        
        lifecycleScope.launch {
            try {
                val result = downloadManager.getProjectsList(pageSize = 5)
                
                if (result.isSuccess) {
                    val response = result.getOrNull()
                    val projectCount = response?.data?.projects?.size ?: 0
                    binding.tvResults.append("✅ 项目列表API成功\n")
                    binding.tvResults.append("获取到 $projectCount 个项目\n")
                    
                    // 显示前几个项目
                    response?.data?.projects?.take(3)?.forEach { project ->
                        binding.tvResults.append("  - ${project.id}: ${project.name}\n")
                    }
                    
                    Log.i(TAG, "项目列表API测试成功: $projectCount 个项目")
                } else {
                    val error = result.exceptionOrNull()?.message ?: "未知错误"
                    binding.tvResults.append("❌ 项目列表API失败: $error\n")
                    Log.e(TAG, "项目列表API测试失败: $error")
                }
                
                binding.tvResults.append("\n")
                
            } catch (e: Exception) {
                binding.tvResults.append("❌ 项目列表API异常: ${e.message}\n\n")
                Log.e(TAG, "项目列表API测试异常", e)
            }
        }
    }
    
    private fun testDownloadAPI() {
        binding.tvResults.append("=== 下载功能测试 ===\n")
        
        lifecycleScope.launch {
            try {
                // 首先获取一个项目ID
                val projectsResult = downloadManager.getProjectsList(pageSize = 1)
                
                if (projectsResult.isSuccess) {
                    val projects = projectsResult.getOrNull()?.data?.projects
                    if (!projects.isNullOrEmpty()) {
                        val testProject = projects.first()
                        binding.tvResults.append("测试项目: ${testProject.id}\n")
                        
                        // 测试下载
                        val downloadResult = downloadManager.downloadProject(testProject.id) { progress ->
                            runOnUiThread {
                                binding.tvResults.append("下载进度: ${(progress * 100).toInt()}%\n")
                            }
                        }
                        
                        if (downloadResult.isSuccess) {
                            binding.tvResults.append("✅ 下载测试成功\n")
                            Log.i(TAG, "下载测试成功: ${testProject.id}")
                        } else {
                            val error = downloadResult.exceptionOrNull()?.message ?: "未知错误"
                            binding.tvResults.append("❌ 下载测试失败: $error\n")
                            Log.e(TAG, "下载测试失败: $error")
                        }
                    } else {
                        binding.tvResults.append("⚠️ 没有可用的项目进行下载测试\n")
                    }
                } else {
                    binding.tvResults.append("❌ 无法获取项目列表进行下载测试\n")
                }
                
                binding.tvResults.append("\n")
                
            } catch (e: Exception) {
                binding.tvResults.append("❌ 下载功能测试异常: ${e.message}\n\n")
                Log.e(TAG, "下载功能测试异常", e)
            }
        }
    }
}
