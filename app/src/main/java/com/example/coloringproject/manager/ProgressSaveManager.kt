package com.example.coloringproject.manager

import android.content.Context
import android.graphics.Bitmap
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.example.coloringproject.data.ColoringData
import com.example.coloringproject.utils.LibraryEventManager
import com.example.coloringproject.utils.LoadResult
import com.example.coloringproject.utils.ProjectSaveManager
import kotlinx.coroutines.*

/**
 * 进度保存管理器
 * 负责处理项目进度保存、预览图生成等数据持久化操作
 */
class ProgressSaveManager(private val context: Context) {
    
    private val TAG = "ProgressSaveManager"
    private val projectSaveManager = ProjectSaveManager(context)
    
    // 预览图片生成的延迟任务
    private var previewGenerationRunnable: Runnable? = null
    private val previewGenerationHandler = Handler(Looper.getMainLooper())
    
    /**
     * 快速保存进度（不生成预览图）
     */
    fun saveProgressFast(
        projectName: String,
        coloringData: ColoringData,
        filledRegions: Set<Int>
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val startTime = System.currentTimeMillis()
                
                val result = projectSaveManager.saveProgressFast(
                    projectName = projectName,
                    coloringData = coloringData,
                    filledRegions = filledRegions
                )
                
                val endTime = System.currentTimeMillis()
                Log.d(TAG, "快速保存完成，耗时: ${endTime - startTime}ms")
                
                withContext(Dispatchers.Main) {
                    when (result) {
                        is com.example.coloringproject.utils.SaveResult.Success -> {
                            val progressPercentage = (filledRegions.size * 100) / coloringData.regions.size
                            LibraryEventManager.notifyProjectProgressUpdated(
                                projectName,
                                filledRegions.isNotEmpty(),
                                progressPercentage
                            )
                            Log.d(TAG, "进度保存成功: $projectName, 进度: $progressPercentage%")
                        }
                        is com.example.coloringproject.utils.SaveResult.Error -> {
                            Log.e(TAG, "进度保存失败: $projectName")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "保存进度时出错: $projectName", e)
            }
        }
    }
    
    /**
     * 调度预览图片生成（延迟执行）
     */
    fun schedulePreviewImageGeneration(
        projectName: String,
        coloringData: ColoringData,
        filledRegions: Set<Int>,
        captureArtwork: () -> Bitmap?
    ) {
        // 取消之前的任务
        previewGenerationRunnable?.let { previewGenerationHandler.removeCallbacks(it) }
        
        // 创建新的延迟任务
        previewGenerationRunnable = Runnable {
            generatePreviewImage(projectName, coloringData, filledRegions, captureArtwork)
        }
        
        // 延迟2秒执行，避免用户连续填色时频繁生成
        previewGenerationHandler.postDelayed(previewGenerationRunnable!!, 2000)
        
        Log.d(TAG, "已调度预览图片生成任务（2秒后执行）")
    }
    
    /**
     * 立即生成预览图片
     */
    fun generatePreviewImageNow(
        projectName: String,
        coloringData: ColoringData,
        filledRegions: Set<Int>,
        captureArtwork: () -> Bitmap?
    ) {
        generatePreviewImage(projectName, coloringData, filledRegions, captureArtwork)
    }
    
    /**
     * 后台保存任务（用于Activity退出时）
     */
    fun startBackgroundSaveTask(
        projectName: String,
        coloringData: ColoringData,
        filledRegions: Set<Int>,
        capturedPreviewBitmap: Bitmap? = null,
        captureArtwork: (() -> Bitmap?)? = null
    ) {
        Log.d(TAG, "启动后台保存任务: $projectName")
        
        // 使用GlobalScope确保即使Activity销毁也能完成保存
        GlobalScope.launch(Dispatchers.IO) {
            try {
                val startTime = System.currentTimeMillis()
                
                // 1. 快速保存进度数据
                val saveResult = projectSaveManager.saveProgressFast(
                    projectName = projectName,
                    coloringData = coloringData,
                    filledRegions = filledRegions
                )
                
                val saveTime = System.currentTimeMillis() - startTime
                Log.d(TAG, "后台进度保存完成: $projectName, 耗时: ${saveTime}ms")
                
                // 2. 处理预览图片
                var previewBitmap: Bitmap? = capturedPreviewBitmap
                
                // 如果没有传入预览图且有捕获函数，尝试捕获
                if (previewBitmap == null && captureArtwork != null) {
                    try {
                        previewBitmap = withContext(Dispatchers.Main) {
                            captureArtwork()
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "从ColoringView捕获预览图失败: $projectName", e)
                    }
                }
                
                // 保存预览图
                if (previewBitmap != null) {
                    try {
                        val thumbnailBitmap = createThumbnailBitmap(previewBitmap)
                        projectSaveManager.savePreviewImage(projectName, thumbnailBitmap)
                        
                        if (thumbnailBitmap != previewBitmap) {
                            previewBitmap.recycle()
                        }
                        
                        Log.d(TAG, "后台预览图生成完成: $projectName")
                    } catch (e: Exception) {
                        Log.e(TAG, "保存预览图失败: $projectName", e)
                    }
                }
                
                // 3. 发送通知
                withContext(Dispatchers.Main) {
                    val progressPercentage = (filledRegions.size * 100) / coloringData.regions.size
                    
                    LibraryEventManager.notifyProjectProgressUpdated(
                        projectName,
                        filledRegions.isNotEmpty(),
                        progressPercentage
                    )
                    
                    val previewPath = projectSaveManager.getPreviewImagePath(projectName)
                    LibraryEventManager.notifyProjectPreviewUpdated(projectName, previewPath)
                    
                    Log.d(TAG, "后台通知发送完成: $projectName")
                }
                
                val totalTime = System.currentTimeMillis() - startTime
                Log.d(TAG, "后台保存任务完全完成: $projectName, 总耗时: ${totalTime}ms")
                
            } catch (e: Exception) {
                Log.e(TAG, "后台保存任务失败: $projectName", e)
            }
        }
    }


    
    /**
     * 加载保存的进度
     */
    suspend fun loadSavedProgress(projectName: String): com.example.coloringproject.utils.LoadResult<com.example.coloringproject.utils.FullProjectProgress> {
        return withContext(Dispatchers.IO) {
            projectSaveManager.loadFullProgress(projectName)
        }
    }
    
    /**
     * 清理预览图生成任务
     */
    fun cleanup() {
        previewGenerationRunnable?.let { previewGenerationHandler.removeCallbacks(it) }
    }
    
    // 私有方法
    
    private fun generatePreviewImage(
        projectName: String,
        coloringData: ColoringData,
        filledRegions: Set<Int>,
        captureArtwork: () -> Bitmap?
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val previewBitmap = withContext(Dispatchers.Main) {
                    captureArtwork()
                }
                
                if (previewBitmap != null) {
                    val thumbnailBitmap = createThumbnailBitmap(previewBitmap)
                    projectSaveManager.savePreviewImage(projectName, thumbnailBitmap)
                    
                    if (thumbnailBitmap != previewBitmap) {
                        previewBitmap.recycle()
                    }
                    
                    Log.d(TAG, "预览图片生成完成: $projectName")
                    
                    // 通知Library刷新预览图片
                    withContext(Dispatchers.Main) {
                        val previewPath = projectSaveManager.getPreviewImagePath(projectName)
                        LibraryEventManager.notifyProjectPreviewUpdated(projectName, previewPath)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "生成预览图片失败: $projectName", e)
            }
        }
    }
    
    private fun createThumbnailBitmap(originalBitmap: Bitmap): Bitmap {
        val maxThumbnailSize = 512
        
        val originalWidth = originalBitmap.width
        val originalHeight = originalBitmap.height
        
        if (originalWidth <= maxThumbnailSize && originalHeight <= maxThumbnailSize) {
            return originalBitmap
        }
        
        val scale = minOf(
            maxThumbnailSize.toFloat() / originalWidth,
            maxThumbnailSize.toFloat() / originalHeight
        )
        
        val newWidth = (originalWidth * scale).toInt()
        val newHeight = (originalHeight * scale).toInt()
        
        val thumbnailBitmap = Bitmap.createScaledBitmap(originalBitmap, newWidth, newHeight, true)
        
        Log.d(TAG, "创建缩略图: ${originalWidth}x${originalHeight} -> ${newWidth}x${newHeight}")
        
        return thumbnailBitmap
    }
}