package com.example.coloringproject

import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.example.coloringproject.databinding.ActivityEnhancedMainBinding
import com.example.coloringproject.manager.ProjectPreloadManager
import com.example.coloringproject.network.ResourceDownloadManager
import com.example.coloringproject.ui.DailyChallengeFragment
import com.example.coloringproject.ui.MyGalleryFragment
import com.example.coloringproject.ui.NewProjectsFragment
import com.example.coloringproject.utils.CategoryManager
import com.example.coloringproject.utils.HybridResourceManager
import com.example.coloringproject.utils.ProjectProgress
import com.example.coloringproject.utils.ProjectSaveManager
import com.example.coloringproject.viewmodel.MyGalleryViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 增强版主Activity
 * 集成了第一阶段的所有新功能
 */
class EnhancedMainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityEnhancedMainBinding
    private var currentFragment: Fragment? = null

    // Fragment缓存机制 - 避免重复创建和加载
    private var cachedLibraryFragment: com.example.coloringproject.ui.CategorizedProjectsFragment? = null
    private var cachedGalleryFragment: MyGalleryFragment? = null
    private var cachedDailyChallengeFragment: DailyChallengeFragment? = null

    // 管理器和ViewModel
    private lateinit var projectSaveManager: ProjectSaveManager
    private lateinit var downloadManager: ResourceDownloadManager
    private lateinit var categoryManager: CategoryManager
    private val galleryViewModel: MyGalleryViewModel by viewModels()
    
    // 内存管理器
//    private val memoryManager = com.example.coloringproject.utils.MemoryManager()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        try {
            // 初始化全局异常处理
//            com.example.coloringproject.utils.CrashHandler.instance.init(this)
            
            // 启用窗口内容过渡
            window.requestFeature(android.view.Window.FEATURE_CONTENT_TRANSITIONS)

            binding = ActivityEnhancedMainBinding.inflate(layoutInflater)
            setContentView(binding.root)

            initManagers()
            setupBottomNavigation()
            
            // 启动内存监控
//            memoryManager.startMemoryMonitoring(this)

            // 默认显示新项目页面
            if (savedInstanceState == null) {
                showNewProjectsSafely()
            }
            
            Log.d("EnhancedMainActivity", "onCreate completed successfully")
        } catch (e: Exception) {
            Log.e("EnhancedMainActivity", "Critical error in onCreate", e)
            handleCriticalError("应用启动失败", e)
        }
    }
    
    private fun handleCriticalError(message: String, error: Exception) {
        try {
            runOnUiThread {
                android.widget.Toast.makeText(this, "$message: ${error.message}", android.widget.Toast.LENGTH_LONG).show()
            }
            Log.e("EnhancedMainActivity", "Critical error: $message", error)
            finish()
        } catch (e: Exception) {
            Log.e("EnhancedMainActivity", "Error handling critical error", e)
            System.exit(1)
        }
    }

    private fun initManagers() {
        try {
            projectSaveManager = ProjectSaveManager(this)
            downloadManager = ResourceDownloadManager(this)
            categoryManager = CategoryManager(this, downloadManager)
            galleryViewModel.setSaveManager(projectSaveManager)
            
            Log.d("EnhancedMainActivity", "All managers initialized successfully")
        } catch (e: Exception) {
            Log.e("EnhancedMainActivity", "Error initializing managers", e)
            // 创建默认的管理器实例，确保应用不会崩溃
            try {
                projectSaveManager = ProjectSaveManager(this)
                downloadManager = ResourceDownloadManager(this)
                Log.w("EnhancedMainActivity", "Fallback managers created")
            } catch (fallbackError: Exception) {
                Log.e("EnhancedMainActivity", "Failed to create fallback managers", fallbackError)
                // 如果连fallback都失败，显示错误并关闭Activity
                showInitializationError()
            }
        }
    }
    
    private fun showInitializationError() {
        runOnUiThread {
            android.widget.Toast.makeText(this, "应用初始化失败，请重启应用", android.widget.Toast.LENGTH_LONG).show()
            finish()
        }
    }

    override fun onBackPressed() {
        // 在退出前自动保存进度
        super.onBackPressed()
    }

    // 防抖机制：避免频繁刷新
    private var lastResumeTime = 0L
    private val resumeDebounceTime = 500L // 500ms防抖

    override fun onResume() {
        super.onResume()

        val currentTime = System.currentTimeMillis()
        if (currentTime - lastResumeTime < resumeDebounceTime) {
            Log.d("EnhancedMainActivity", "onResume被防抖机制拦截")
            return
        }
        lastResumeTime = currentTime

        // 确保Fragment回调有效
        ensureNewProjectsFragmentCallback()

        // 延迟刷新，避免在Activity切换过程中执行
        lifecycleScope.launch {
            delay(100) // 短暂延迟

            try {
                // 刷新Gallery状态，确保缩略图更新
                if (currentFragment is MyGalleryFragment) {
                    Log.d("EnhancedMainActivity", "刷新Gallery")
                    galleryViewModel.refreshProjectProgress()
                }

                // 刷新Library中的项目缩略图
                if (currentFragment is com.example.coloringproject.ui.CategorizedProjectsFragment) {
                    Log.d("EnhancedMainActivity", "刷新Library")
                    refreshCategorizedProjectsFragment()
                }
            } catch (e: Exception) {
                Log.e("EnhancedMainActivity", "onResume刷新时出错", e)
            }
        }
    }

    override fun onPause() {
        super.onPause()
        // 应用暂停时刷新Gallery状态
        if (currentFragment is MyGalleryFragment) {
            galleryViewModel.refreshProjectProgress()
        }
    }
    
    private fun setupBottomNavigation() {
        binding.bottomNavigation.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.nav_new_projects -> {
                    showNewProjects()
                    true
                }
                R.id.nav_daily_challenge -> {
                    showDailyChallenge()
                    true
                }
                R.id.nav_my_gallery -> {
                    showMyGallery()
                    true
                }
                else -> false
            }
        }
    }
    
    private fun showNewProjectsSafely() {
        try {
            showNewProjects()
        } catch (e: Exception) {
            Log.e("EnhancedMainActivity", "Error showing new projects", e)
            showErrorFragment("加载项目列表失败")
        }
    }
    
    private fun showNewProjects() {
        Log.d("EnhancedMainActivity", "showNewProjects被调用")

        try {
            // 检查Activity状态
            if (isDestroyed || isFinishing) {
                Log.w("EnhancedMainActivity", "Activity is destroyed or finishing, skipping fragment operation")
                return
            }

            // 使用缓存机制避免重复创建
            val fragment = if (cachedLibraryFragment != null && !cachedLibraryFragment!!.isDetached) {
                Log.d("EnhancedMainActivity", "重用缓存的CategorizedProjectsFragment")
                cachedLibraryFragment!!
            } else {
                Log.d("EnhancedMainActivity", "创建新的CategorizedProjectsFragment并缓存")
                val newFragment = com.example.coloringproject.ui.CategorizedProjectsFragment.newInstance()
                cachedLibraryFragment = newFragment
                newFragment
            }

            // 确保回调始终有效
            Log.d("EnhancedMainActivity", "设置onProjectSelected回调")
            fragment.onProjectSelected = { project, imageView ->
                Log.d("EnhancedMainActivity", "收到项目选择回调: ${project.id}")
                // 安全地启动填色Activity
                startColoringActivitySafely(project, imageView)
            }

            // 使用show/hide机制而不是replace
            showFragmentWithCache(fragment)

            Log.d("EnhancedMainActivity", "showNewProjects完成")
        } catch (e: Exception) {
            Log.e("EnhancedMainActivity", "Error in showNewProjects", e)
            showErrorFragment("加载新项目失败")
        }
    }
    
    private fun showErrorFragment(message: String) {
        try {
            // 创建一个简单的错误显示Fragment或View
            runOnUiThread {
                binding.fragmentContainer.removeAllViews()
                val errorView = android.widget.TextView(this).apply {
                    text = message
                    gravity = android.view.Gravity.CENTER
                    textSize = 16f
                    setTextColor(android.graphics.Color.RED)
                }
                binding.fragmentContainer.addView(errorView)
            }
        } catch (e: Exception) {
            Log.e("EnhancedMainActivity", "Error showing error fragment", e)
        }
    }

    private fun showDailyChallenge() {
        try {
            // 使用缓存机制避免重复创建
            val fragment = if (cachedDailyChallengeFragment != null && !cachedDailyChallengeFragment!!.isDetached) {
                Log.d("EnhancedMainActivity", "重用缓存的DailyChallengeFragment")
                cachedDailyChallengeFragment!!
            } else {
                Log.d("EnhancedMainActivity", "创建新的DailyChallengeFragment并缓存")
                val newFragment = DailyChallengeFragment.newInstance()
                cachedDailyChallengeFragment = newFragment
                newFragment
            }

            fragment.onProjectSelected = { project ->
                // 安全地启动填色Activity
                startColoringActivitySimpleSafely(project.name)
            }

            // 使用show/hide机制而不是replace
            showFragmentWithCache(fragment)

            // 在Fragment添加到容器后设置DownloadManager
            try {
                fragment.setDownloadManager(downloadManager)
            } catch (e: Exception) {
                Log.e("EnhancedMainActivity", "Error setting download manager", e)
            }
        } catch (e: Exception) {
            Log.e("EnhancedMainActivity", "Error showing daily challenge", e)
            showErrorFragment("加载每日挑战失败")
        }
    }

    private fun startColoringActivitySimpleSafely(projectName: String) {
        try {
            startColoringActivitySimple(projectName)
        } catch (e: Exception) {
            Log.e("EnhancedMainActivity", "Error starting simple coloring activity", e)
            runOnUiThread {
                android.widget.Toast.makeText(this, "启动填色页面失败", android.widget.Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun startColoringActivitySimple(projectName: String) {
        try {
            if (isDestroyed || isFinishing) {
                Log.w("EnhancedMainActivity", "Activity is destroyed or finishing, skipping activity start")
                return
            }
            
            val intent = android.content.Intent(this, RefactoredSimpleMainActivity::class.java)
            intent.putExtra("project_name", projectName)
            startActivity(intent)
            
            Log.d("EnhancedMainActivity", "Simple coloring activity started for: $projectName")
        } catch (e: Exception) {
            Log.e("EnhancedMainActivity", "Failed to start simple coloring activity", e)
            throw e
        }
    }

    private fun showMyGallery() {
        try {
            // 使用缓存机制避免重复创建
            val fragment = if (cachedGalleryFragment != null && !cachedGalleryFragment!!.isDetached) {
                Log.d("EnhancedMainActivity", "重用缓存的MyGalleryFragment")
                cachedGalleryFragment!!
            } else {
                Log.d("EnhancedMainActivity", "创建新的MyGalleryFragment并缓存")
                val newFragment = MyGalleryFragment.newInstance()
                cachedGalleryFragment = newFragment
                newFragment
            }

            fragment.onProjectSelected = { project, imageView ->
                // 安全地启动填色Activity - 这里project是HybridProject类型
                startColoringActivitySafely(project, imageView)
            }

            // 使用show/hide机制而不是replace
            showFragmentWithCache(fragment)
        } catch (e: Exception) {
            Log.e("EnhancedMainActivity", "Error showing my gallery", e)
            showErrorFragment("加载我的作品失败")
        }
    }

    private fun startColoringActivityWithProgress(project: ProjectProgress, imageView: android.widget.ImageView) {
        try {
            // 检查Activity状态
            if (isDestroyed || isFinishing) {
                Log.w("EnhancedMainActivity", "Activity is destroyed or finishing, skipping activity start")
                return
            }
            
            val intent = android.content.Intent(this, RefactoredSimpleMainActivity::class.java)
            intent.putExtra("project_name", project.projectName)
            intent.putExtra("from_gallery", true) // 标记来自Gallery
            // 只要有填色区域就认为有进度，不依赖progressPercentage
            intent.putExtra("has_progress", project.filledRegions.isNotEmpty())

            // 安全地创建共享元素过渡动画
            val options = try {
                android.app.ActivityOptions.makeSceneTransitionAnimation(
                    this,
                    imageView,
                    "project_image_${project.projectName}"
                )
            } catch (e: Exception) {
                Log.w("EnhancedMainActivity", "Failed to create shared element transition for gallery project", e)
                null
            }

            if (options != null) {
                startActivity(intent, options.toBundle())
            } else {
                startActivity(intent)
            }

            val message = if (project.progressPercentage > 0) {
                "继续填色: ${project.projectName} (${project.progressPercentage}%)"
            } else {
                "开始填色: ${project.projectName}"
            }

            Log.d("EnhancedMainActivity", "启动Gallery项目: ${project.projectName}, 有进度: ${project.filledRegions.isNotEmpty()}, 进度: ${project.progressPercentage}%")
        } catch (e: Exception) {
            Log.e("EnhancedMainActivity", "Failed to start coloring activity with progress", e)
            throw e
        }
    }

    private fun startColoringActivitySafely(project: HybridResourceManager.HybridProject, imageView: android.widget.ImageView) {
        try {
            startColoringActivityWithProject(project, imageView)
        } catch (e: Exception) {
            Log.e("EnhancedMainActivity", "Error starting coloring activity", e)
            // 回退到简单启动方式
            startColoringActivityFallback(project)
        }
    }
    
    private fun startColoringActivityFallback(project: HybridResourceManager.HybridProject) {
        try {
            val intent = Intent(this, RefactoredSimpleMainActivity::class.java)
            intent.putExtra("project_id", project.id)
            intent.putExtra("project_name", project.displayName ?: project.id)
            startActivity(intent)
            
            Log.i("EnhancedMainActivity", "Fallback activity started for project: ${project.id}")
        } catch (e: Exception) {
            Log.e("EnhancedMainActivity", "Fallback activity start failed", e)
            runOnUiThread {
                android.widget.Toast.makeText(this, "启动填色页面失败", android.widget.Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 启动填色Activity - 传递完整项目信息，支持共享元素过渡
     */
    private fun startColoringActivityWithProject(project: HybridResourceManager.HybridProject, imageView: android.widget.ImageView) {
        Log.d("MEMORY_CLEANUP", "=== 准备启动填色Activity ===")
        Log.d("MEMORY_CLEANUP", "项目ID: ${project.id}")

        // 检查Activity状态
        if (isDestroyed || isFinishing) {
            Log.w("EnhancedMainActivity", "Activity is destroyed or finishing, skipping activity start")
            return
        }

        // 智能内存管理：检查预加载数据和内存状态
        try {
            performMemoryOptimization(project.id)
        } catch (e: Exception) {
            Log.e("MEMORY_CLEANUP", "内存优化失败", e)
            // 继续执行，不因为内存优化失败而阻止启动
        }

        try {
            val intent = Intent(this, RefactoredSimpleMainActivity::class.java)
            intent.putExtra("project_id", project.id)
            intent.putExtra("project_name", project.displayName ?: project.id)
            intent.putExtra("project_source", project.resourceSource.name)
            
            Log.i("EnhancedMainActivity", "启动RefactoredSimpleMainActivity: id=${project.id}, name=${project.displayName}, source=${project.resourceSource.name}")

            // 安全地创建共享元素过渡动画
            val options = try {
                android.app.ActivityOptions.makeSceneTransitionAnimation(
                    this,
                    imageView,
                    "project_image_${project.id}"
                )
            } catch (e: Exception) {
                Log.w("EnhancedMainActivity", "Failed to create shared element transition", e)
                null
            }

            if (options != null) {
                startActivity(intent, options.toBundle())
            } else {
                startActivity(intent)
            }
        } catch (e: Exception) {
            Log.e("EnhancedMainActivity", "Failed to start coloring activity", e)
            throw e
        }
    }
    
    private fun performMemoryOptimization(projectId: String) {
        try {
            // 检查是否有预加载数据
            val preloadManager = ProjectPreloadManager.getInstance(this)
            val hasPreloadedData = checkHasPreloadedData(projectId, preloadManager)

            // 获取当前内存状态
            val runtime = Runtime.getRuntime()
            val usedMemory = runtime.totalMemory() - runtime.freeMemory()
            val maxMemory = runtime.maxMemory()
            val availableMemory = maxMemory - usedMemory
            val memoryUsagePercent = (usedMemory.toFloat() / maxMemory * 100).toInt()

            Log.d("MEMORY_CLEANUP", "内存状态检查: 已用=${usedMemory / 1024 / 1024}MB, 可用=${availableMemory / 1024 / 1024}MB, 使用率=${memoryUsagePercent}%")
            Log.d("MEMORY_CLEANUP", "预加载数据状态: ${if (hasPreloadedData) "存在" else "不存在"}")

            // 如果有预加载数据，提高清理阈值，避免影响性能
            val cleanupThreshold = if (hasPreloadedData) 85 else 75
            val memoryThreshold = if (hasPreloadedData) 20 * 1024 * 1024 else 30 * 1024 * 1024

            // 只有在内存使用率超过阈值时才清理
            if (memoryUsagePercent > cleanupThreshold || availableMemory < memoryThreshold) {
                Log.w("MEMORY_CLEANUP", "⚠️ 内存使用率过高(${memoryUsagePercent}%)，执行清理")
                clearMemoryCache()

                // 只在内存严重不足时才强制垃圾回收
                if (availableMemory < 15 * 1024 * 1024) {
                    Log.w("MEMORY_CLEANUP", "⚠️ 内存严重不足，执行垃圾回收")
                    System.gc()
                }
            } else {
                Log.d("MEMORY_CLEANUP", "✅ 内存充足，跳过清理")
            }
        } catch (e: Exception) {
            Log.e("MEMORY_CLEANUP", "内存优化过程中出错", e)
            throw e
        }
    }
    
    /**
     * 使用缓存机制显示Fragment，避免重复创建和加载
     */
    private fun showFragmentWithCache(fragment: Fragment) {
        try {
            // 检查Activity状态
            if (isDestroyed || isFinishing) {
                Log.w("EnhancedMainActivity", "Activity is destroyed or finishing, skipping fragment operation")
                return
            }

            // 检查FragmentManager状态
            if (supportFragmentManager.isStateSaved) {
                Log.w("EnhancedMainActivity", "FragmentManager state is saved, deferring fragment transaction")
                return
            }

            val transaction = supportFragmentManager.beginTransaction()

            // 隐藏当前显示的Fragment
            currentFragment?.let { current ->
                if (current.isAdded) {
                    transaction.hide(current)
                    Log.d("EnhancedMainActivity", "隐藏Fragment: ${current.javaClass.simpleName}")
                }
            }

            // 显示目标Fragment
            if (fragment.isAdded) {
                // Fragment已添加，只需显示
                transaction.show(fragment)
                Log.d("EnhancedMainActivity", "显示已存在的Fragment: ${fragment.javaClass.simpleName}")
            } else {
                // Fragment未添加，需要先添加
                transaction.add(binding.fragmentContainer.id, fragment)
                Log.d("EnhancedMainActivity", "添加新Fragment: ${fragment.javaClass.simpleName}")
            }

            transaction.commitAllowingStateLoss()
            currentFragment = fragment

            Log.d("EnhancedMainActivity", "Fragment切换完成: ${fragment.javaClass.simpleName}")
        } catch (e: Exception) {
            Log.e("EnhancedMainActivity", "Error showing fragment with cache", e)
            // 降级到原有的replace机制
            replaceFragmentSafely(fragment)
        }
    }

    private fun replaceFragmentSafely(fragment: Fragment) {
        try {
            replaceFragment(fragment)
        } catch (e: Exception) {
            Log.e("EnhancedMainActivity", "Error replacing fragment", e)
            showErrorFragment("切换页面失败")
        }
    }
    
    private fun replaceFragment(fragment: Fragment) {
        try {
            // 检查Activity状态
            if (isDestroyed || isFinishing) {
                Log.w("EnhancedMainActivity", "Activity is destroyed or finishing, skipping fragment transaction")
                return
            }
            
            // 检查FragmentManager状态
            if (supportFragmentManager.isStateSaved) {
                Log.w("EnhancedMainActivity", "FragmentManager state is saved, deferring fragment transaction")
                // 延迟执行Fragment事务
                lifecycleScope.launch {
                    try {
                        kotlinx.coroutines.delay(100)
                        if (!isDestroyed && !isFinishing && !supportFragmentManager.isStateSaved) {
                            replaceFragment(fragment)
                        }
                    } catch (e: Exception) {
                        Log.e("EnhancedMainActivity", "Error in deferred fragment transaction", e)
                    }
                }
                return
            }
            
            currentFragment = fragment
            supportFragmentManager.beginTransaction()
                .replace(binding.fragmentContainer.id, fragment)
                .commitAllowingStateLoss() // 使用commitAllowingStateLoss避免状态丢失异常
                
            Log.d("EnhancedMainActivity", "Fragment replaced successfully: ${fragment.javaClass.simpleName}")
        } catch (e: Exception) {
            Log.e("EnhancedMainActivity", "Error in replaceFragment", e)
            throw e
        }
    }


    /**
     * 重新设置NewProjectsFragment的回调（用于Fragment重建后的回调恢复）
     */
    fun ensureNewProjectsFragmentCallback() {
        Log.d("CALLBACK_FIX", "ensureNewProjectsFragmentCallback被调用")
        Log.d("CALLBACK_FIX", "currentFragment变量: ${currentFragment?.javaClass?.simpleName}")

        // 直接从FragmentManager获取当前Fragment，而不是依赖currentFragment变量
        val actualFragment = supportFragmentManager.findFragmentById(binding.fragmentContainer.id)
        Log.d("CALLBACK_FIX", "FragmentManager中的实际Fragment: ${actualFragment?.javaClass?.simpleName}")

        when (actualFragment) {
            is NewProjectsFragment -> {
                Log.d("CALLBACK_FIX", "✅ 找到NewProjectsFragment，重新设置回调")
                actualFragment.onProjectSelected = { project, imageView ->
                    Log.d("CALLBACK_FIX", "收到项目选择回调: ${project.id}")
                    startColoringActivityWithProject(project, imageView)
                }
                Log.d("CALLBACK_FIX", "✅ NewProjectsFragment回调设置完成")
                currentFragment = actualFragment
            }
            is com.example.coloringproject.ui.CategorizedProjectsFragment -> {
                Log.d("CALLBACK_FIX", "✅ 找到CategorizedProjectsFragment，重新设置回调")
                actualFragment.onProjectSelected = { project, imageView ->
                    Log.d("CALLBACK_FIX", "收到项目选择回调: ${project.id}")
                    startColoringActivityWithProject(project, imageView)
                }
                Log.d("CALLBACK_FIX", "✅ CategorizedProjectsFragment回调设置完成")
                currentFragment = actualFragment
            }
            else -> {
                Log.w("CALLBACK_FIX", "⚠️ 当前Fragment不是已知的项目Fragment，无法设置回调")
                Log.w("CALLBACK_FIX", "实际Fragment类型: ${actualFragment?.javaClass?.name}")
            }
        }
    }

    /**
     * 刷新分类项目Fragment中的缩略图
     */
    private fun refreshCategorizedProjectsFragment() {
        // 使用协程延迟执行，确保Fragment完全加载
        lifecycleScope.launch {
            try {
                // 延迟一段时间，确保Fragment切换完成
                delay(200)

                val fragment = supportFragmentManager.findFragmentById(binding.fragmentContainer.id)
                if (fragment is com.example.coloringproject.ui.CategorizedProjectsFragment &&
                    fragment.isAdded && !fragment.isDetached) {
                    Log.d("REFRESH", "刷新CategorizedProjectsFragment中的缩略图")
                    // 通知Fragment刷新所有分类的项目数据
                    fragment.refreshAllCategories()
                } else {
                    Log.d("REFRESH", "Fragment不是CategorizedProjectsFragment或不活跃，跳过刷新")
                }
            } catch (e: Exception) {
                Log.e("REFRESH", "刷新CategorizedProjectsFragment时出错", e)
            }
        }
    }

    /**
     * 清理内存缓存（保守策略，保护预加载数据）
     */
    private fun clearMemoryCache() {
        try {
            Log.d("MEMORY_CLEANUP", "开始轻量级内存清理...")

            // 清理Fragment中的缓存（轻量级清理）
            val fragment = supportFragmentManager.findFragmentById(binding.fragmentContainer.id) as? NewProjectsFragment
            fragment?.let {
                Log.d("MEMORY_CLEANUP", "清理Fragment状态缓存")
                it.clearFragmentCache()
            }

            // 只清理ProjectSaveManager的解析缓存，不影响预加载数据
            // 注意：这里不清理ProjectPreloadManager的缓存，保护预加载数据
            projectSaveManager.clearCache()

            Log.d("MEMORY_CLEANUP", "轻量级内存清理完成")
        } catch (e: Exception) {
            Log.e("MEMORY_CLEANUP", "清理内存缓存失败", e)
        }
    }

    /**
     * 强制内存清理（仅在内存严重不足时使用）
     */
    private fun forceMemoryCleanup() {
        try {
            Log.d("MEMORY_CLEANUP", "执行强制内存清理...")

            // 先清理缓存
            clearMemoryCache()

            // 单次垃圾回收，避免过度影响性能
            System.gc()
            Log.d("MEMORY_CLEANUP", "执行垃圾回收")

            Log.d("MEMORY_CLEANUP", "强制内存清理完成")
        } catch (e: Exception) {
            Log.e("MEMORY_CLEANUP", "强制内存清理失败", e)
        }
    }

    /**
     * 检查是否有预加载数据
     */
    private fun checkHasPreloadedData(projectId: String, preloadManager: ProjectPreloadManager): Boolean {
        return try {
            // 这里需要添加一个方法来检查ProjectPreloadManager是否有特定项目的数据
            // 由于ProjectPreloadManager的preloadedProjects是私有的，我们需要添加一个公共方法
            // 暂时返回false，避免编译错误
            false
        } catch (e: Exception) {
            Log.e("MEMORY_CLEANUP", "检查预加载数据失败", e)
            false
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        try {
            // 清理Fragment缓存
            clearFragmentCache()

            // 停止内存监控
//            memoryManager.stopMemoryMonitoring()

            // 清理资源
            try {
                if (::projectSaveManager.isInitialized) {
                    projectSaveManager.clearCache()
                }
            } catch (e: Exception) {
                Log.e("EnhancedMainActivity", "Error clearing project save manager cache", e)
            }

            // 清理Fragment引用
            currentFragment = null

            Log.d("EnhancedMainActivity", "EnhancedMainActivity destroyed and resources cleaned up")
        } catch (e: Exception) {
            Log.e("EnhancedMainActivity", "Error during onDestroy cleanup", e)
        }
    }

    /**
     * 清理Fragment缓存，释放内存
     */
    private fun clearFragmentCache() {
        try {
            Log.d("EnhancedMainActivity", "清理Fragment缓存")

            cachedLibraryFragment = null
            cachedGalleryFragment = null
            cachedDailyChallengeFragment = null

            Log.d("EnhancedMainActivity", "Fragment缓存清理完成")
        } catch (e: Exception) {
            Log.e("EnhancedMainActivity", "清理Fragment缓存失败", e)
        }
    }
}
