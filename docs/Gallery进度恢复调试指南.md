# Gallery进度恢复调试指南

## 问题确认

Gallery点击项目确实使用的是`RefactoredSimpleMainActivity`，但进度恢复失败的原因是Intent参数传递不完整。

## 修复内容

### 1. 修复EnhancedMainActivity中的Intent参数传递

**问题**：Gallery项目启动时缺少`from_gallery`和`has_progress`参数

**修复**：
```kotlin
// 检查是否来自Gallery（通过resourceSource判断）
val isFromGallery = project.resourceSource == HybridResourceManager.Companion.ResourceSource.DOWNLOADED
if (isFromGallery) {
    intent.putExtra("from_gallery", true)
    intent.putExtra("has_progress", project.hasPreloadedData) // 有预加载数据说明有进度
    Log.i("EnhancedMainActivity", "Gallery项目启动: from_gallery=true, has_progress=${project.hasPreloadedData}")
}
```

### 2. 增强RefactoredSimpleMainActivity中的调试日志

**添加详细的Intent参数日志**：
```kotlin
Log.d(TAG, "=== Intent参数详细信息 ===")
Log.d(TAG, "projectId: $projectId")
Log.d(TAG, "projectName: $projectName") 
Log.d(TAG, "projectSource: $projectSource")
Log.d(TAG, "fromGallery: $fromGallery")
Log.d(TAG, "hasProgress: $hasProgress")
Log.d(TAG, "========================")
```

## 调试步骤

### 步骤1：验证Intent参数传递
1. 从Gallery点击一个有进度的项目
2. 查看日志中的Intent参数：
```
RefactoredSimpleMainActivity: === Intent参数详细信息 ===
RefactoredSimpleMainActivity: projectId: [项目ID]
RefactoredSimpleMainActivity: projectName: [项目名称]
RefactoredSimpleMainActivity: projectSource: DOWNLOADED
RefactoredSimpleMainActivity: fromGallery: true
RefactoredSimpleMainActivity: hasProgress: true
RefactoredSimpleMainActivity: ========================
```

### 步骤2：验证Gallery加载流程
查看以下日志序列：
```
RefactoredSimpleMainActivity: 从Gallery加载项目: [项目名称], 有进度: true
RefactoredSimpleMainActivity: 使用统一项目名称加载进度: [原名称] -> [统一名称]
RefactoredSimpleMainActivity: 成功加载进度数据: [N]个填色区域
RefactoredSimpleMainActivity: 项目快速设置完成，恢复了[N]个填色区域
```

### 步骤3：验证项目名称一致性
确认以下名称是否一致：
1. **Gallery中显示的项目名称**
2. **Intent传递的project_name**
3. **统一化后的项目名称**
4. **进度保存时使用的项目名称**

## 可能的问题点

### 问题1：项目名称不一致
**症状**：Intent参数正确，但找不到保存的进度
**解决**：检查`ProjectNameUtils.getUnifiedProjectId()`的处理逻辑

### 问题2：resourceSource判断错误
**症状**：Gallery项目的resourceSource不是DOWNLOADED
**解决**：检查`convertToHybridProject()`中的resourceSource设置

### 问题3：hasPreloadedData标记错误
**症状**：项目有进度但hasPreloadedData为false
**解决**：检查Gallery中的进度判断逻辑

## 测试用例

### 测试用例1：新项目（无进度）
1. 从Library选择新项目并填色几个区域
2. 退出到Gallery
3. 从Gallery重新进入
4. **期望**：正确恢复填色进度

### 测试用例2：已有进度项目
1. 从Gallery选择一个已有进度的项目
2. **期望**：立即显示之前的填色进度

### 测试用例3：项目名称特殊字符
1. 测试包含特殊字符的项目名称
2. **期望**：项目名称统一化后能正确匹配

## 日志关键词

搜索以下关键词来定位问题：
- `Intent参数详细信息`
- `从Gallery加载项目`
- `使用统一项目名称加载进度`
- `成功加载进度数据`
- `项目快速设置完成`
- `无法从保存进度加载`

## 预期修复效果

修复后，Gallery进度恢复应该：
1. **正确传递Intent参数**：`from_gallery=true`, `has_progress=true`
2. **使用统一项目名称**：确保保存和加载使用相同的标识符
3. **快速恢复进度**：使用`setupProjectFast()`方法
4. **正确显示UI**：颜色面板显示进度，选中未完成的颜色

这些修复确保了Gallery和Library使用相同的项目加载和进度恢复机制。