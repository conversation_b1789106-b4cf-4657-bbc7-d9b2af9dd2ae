# 项目名称映射修复

## 问题分析

从错误日志可以看出：
- **传递的项目名称**：`animal-2`
- **实际查找的文件**：`Animal 2.json` 和 `Animal 2.png`
- **实际存在的文件**：`animal2.json` 和 `animal2.png`

这是一个典型的显示名称与文件名不匹配的问题。

## 根本原因

1. **Library/Gallery显示名称**：`animal-2`（带连字符）
2. **Assets实际文件名**：`animal2.json`, `animal2.png`（无连字符）
3. **进度保存名称**：使用显示名称 `animal-2`

## 修复方案

### 双重名称策略

1. **文件加载**：使用标准化名称（`animal2`）
2. **进度保存/加载**：使用原始名称（`animal-2`）

### 实现逻辑

```kotlin
private fun loadProjectWithSmartProgressDetection(projectId: String, projectName: String?, projectSource: String?) {
    // 原始名称（用于进度）
    val rawProjectName = projectName ?: projectId
    
    // 标准化名称（用于文件）
    val finalProjectName = normalizeProjectName(rawProjectName)
    
    // 进度检测：使用原始名称
    val progressResult = progressSaveManager.loadSavedProgress(rawProjectName)
    
    // 文件加载：使用标准化名称
    val projectResult = projectLoadManager.loadProject(finalProjectName, projectSource)
}
```

### 名称标准化规则

```kotlin
private fun normalizeProjectName(projectName: String): String {
    return when {
        // animal-1 -> animal1
        projectName.matches(Regex("animal-\\d+")) -> {
            projectName.replace("-", "")
        }
        // "Animal 2" -> "animal2"
        projectName.contains(" ") -> {
            projectName.replace(" ", "").lowercase()
        }
        else -> projectName
    }
}
```

## 调试日志

修复后的日志输出：
```
=== 项目名称一致性检查 ===
输入 - projectId: animal-2, projectName: animal-2
原始项目名称: animal-2
标准化后名称: animal2
用于文件加载: animal2
用于进度保存/加载: animal-2
===============================
```

## 测试验证

### 测试用例1：animal-2项目
1. **输入**：`animal-2`
2. **文件加载**：查找 `animal2.json`, `animal2.png`
3. **进度保存**：使用 `animal-2` 作为键
4. **预期结果**：能找到文件，进度正确保存/恢复

### 测试用例2：其他项目
1. **输入**：`project-name`
2. **标准化**：根据规则转换
3. **验证**：文件能找到，进度能恢复

## 扩展性

如果有其他项目名称映射问题，可以在 `normalizeProjectName()` 中添加新的规则：

```kotlin
private fun normalizeProjectName(projectName: String): String {
    return when {
        // 现有规则
        projectName.matches(Regex("animal-\\d+")) -> projectName.replace("-", "")
        
        // 新增规则
        projectName.startsWith("flower-") -> projectName.replace("-", "_")
        projectName.contains("space") -> projectName.replace(" ", "")
        
        else -> projectName
    }
}
```

## 优势

1. **向后兼容**：不影响现有的进度保存
2. **灵活映射**：可以处理各种名称格式差异
3. **清晰分离**：文件名和进度名分别处理
4. **易于调试**：详细的日志输出

这个修复确保了项目能正确加载，同时保持了进度保存的一致性。