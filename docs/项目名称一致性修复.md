# 项目名称一致性修复

## 问题根源

之前的逻辑中存在多个不同的项目名称处理方式：
1. `unifiedProjectId` - 用于进度加载/保存
2. `actualProjectName` - 用于项目文件加载
3. `getStandardizedProjectName()` - 用于进度保存

这导致了项目名称不匹配，进度无法正确恢复。

## 修复方案

### 核心原则：一个项目，一个名称

**统一使用 `finalProjectName = projectName ?: projectId`**

### 修复后的流程

```kotlin
private fun loadProjectWithSmartProgressDetection(projectId: String, projectName: String?, projectSource: String?) {
    // 步骤1：确定唯一的项目标识符
    val finalProjectName = projectName ?: projectId
    
    // 步骤2：使用相同名称检测进度
    val progressResult = progressSaveManager.loadSavedProgress(finalProjectName)
    
    // 步骤3：使用相同名称加载项目文件
    val projectResult = projectLoadManager.loadProject(finalProjectName, projectSource)
    
    // 步骤4：使用相同名称保存进度（通过getStandardizedProjectName）
}
```

### 名称一致性保证

1. **项目加载**：使用 `finalProjectName`
2. **进度检测**：使用 `finalProjectName`
3. **进度保存**：使用 `getStandardizedProjectName()` 返回相同的 `finalProjectName`

### 调试日志

添加了详细的名称一致性检查日志：
```
=== 项目名称一致性检查 ===
输入 - projectId: [值], projectName: [值]
最终项目名称: [统一名称]
用于文件加载: [统一名称]
用于进度保存/加载: [统一名称]
===============================
```

## 测试验证

### 验证步骤

1. **从Library选择项目**：
   - 查看日志确认所有操作使用相同的项目名称
   - 验证进度能正确保存和恢复

2. **从Gallery选择项目**：
   - 查看日志确认项目名称一致
   - 验证能找到对应的项目文件

### 预期日志输出

```
RefactoredSimpleMainActivity: === 项目名称一致性检查 ===
RefactoredSimpleMainActivity: 输入 - projectId: animal-1, projectName: animal-1
RefactoredSimpleMainActivity: 最终项目名称: animal-1
RefactoredSimpleMainActivity: 用于文件加载: animal-1
RefactoredSimpleMainActivity: 用于进度保存/加载: animal-1
RefactoredSimpleMainActivity: ===============================
RefactoredSimpleMainActivity: 进度检测结果: hasProgress=true (使用名称: animal-1)
RefactoredSimpleMainActivity: 获取标准化项目名称: animal-1 (用于进度保存)
```

## 关键修改

### 1. 统一项目名称确定逻辑
```kotlin
val finalProjectName = projectName ?: projectId
```

### 2. 统一进度保存名称
```kotlin
private fun getStandardizedProjectName(): String {
    val projectId = intent.getStringExtra("project_id")
    val projectName = intent.getStringExtra("project_name")
    val finalProjectName = projectName ?: projectId ?: "unknown"
    return finalProjectName
}
```

### 3. 移除复杂的名称解析逻辑
- 移除了 `resolveActualProjectName()`
- 移除了 `ProjectNameUtils.getUnifiedProjectId()` 的复杂调用
- 简化为直接的名称选择逻辑

## 优势

1. **简单明确**：一个项目只有一个名称
2. **易于调试**：所有日志显示相同的项目名称
3. **避免错误**：消除了名称不匹配的可能性
4. **性能提升**：减少了复杂的名称解析逻辑

## 兼容性

这个修复保持了与现有系统的兼容性：
- Library传递的参数格式不变
- Gallery传递的参数格式不变
- 进度保存格式不变

只是确保了在整个流程中使用一致的项目名称。