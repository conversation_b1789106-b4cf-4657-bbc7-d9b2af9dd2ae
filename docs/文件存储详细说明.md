# 文件存储详细说明

## 文件存储概览

### 1. Assets文件（只读，不会修改）

**位置**：`app/src/main/assets/`
**内容**：项目的原始数据文件
**特点**：
- ✅ **只读**：永远不会被修改或重新存储
- ✅ **打包时固定**：随APK一起分发
- ✅ **原始数据**：包含项目的基础信息

**文件示例**：
```
assets/
├── animal-1.json    # 项目数据（区域、颜色等）
├── animal-1.png     # 轮廓图片
├── animal-2.json
├── animal-2.png
└── ...
```

### 2. 进度文件（动态创建和更新）

**位置**：`/data/data/com.example.coloringproject/files/coloring_saves/`
**内容**：用户的填色进度
**特点**：
- 🔄 **动态创建**：首次填色时创建
- 🔄 **持续更新**：每次填色都会更新
- 💾 **持久保存**：卸载应用前一直保存

**文件格式**：
```
coloring_saves/
├── animal-1_full_progress.json     # 完整进度数据
├── animal-1_preview.png           # 预览图片
├── animal-2_full_progress.json
├── animal-2_preview.png
└── ...
```

## 详细存储机制

### Assets文件处理

```kotlin
// 只读操作，从assets加载
val coloringDataResult = enhancedAssetManager.loadColoringData("animal-1.json")
val outlineResult = enhancedAssetManager.loadOutlineBitmap("animal-1.png")
```

**特点**：
- ❌ **不会重新存储**：assets文件永远不变
- ✅ **每次读取**：需要时从assets读取
- ✅ **内存缓存**：可能有临时缓存，但不持久化

### 进度文件处理

#### 创建时机
```kotlin
// 首次填色时创建
progressSaveManager.saveProgressFast(
    projectName = "animal-1",
    coloringData = coloringData,
    filledRegions = setOf(1, 2, 3)
)
```

#### 更新时机
```kotlin
// 每次填色都会更新
override fun onRegionFilled(regionId: Int) {
    // 立即更新进度文件
    progressSaveManager.saveProgressFast(...)
}
```

#### 文件内容示例
```json
{
  "projectName": "animal-1",
  "filledRegions": [1, 2, 3, 5, 8],
  "totalRegions": 50,
  "lastModified": 1703123456789,
  "progressPercentage": 10,
  "isCompleted": false,
  "coloringData": { /* 完整的项目数据 */ }
}
```

## 存储策略

### 1. 快速保存策略
```kotlin
fun saveProgressFast() {
    // 只保存JSON进度文件
    val fullProgressFile = File(saveDir, "${projectName}_full_progress.json")
    fullProgressFile.bufferedWriter().use { it.write(fullProgressJson) }
}
```

**特点**：
- ⚡ **极快**：1秒内完成
- 📝 **只保存进度**：不生成预览图
- 🔄 **实时更新**：每次填色都触发

### 2. 完整保存策略
```kotlin
fun saveProgress() {
    // 保存进度 + 生成预览图
    saveProgressFast()
    savePreviewImage()
}
```

**特点**：
- 🖼️ **包含预览图**：生成缩略图
- ⏱️ **较慢**：需要2-3秒
- 🎯 **特定时机**：退出时、完成时

## 文件生命周期

### 新建 vs 更新

#### 首次使用项目
1. **Assets读取**：从 `animal-1.json` 读取原始数据
2. **进度文件创建**：创建 `animal-1_full_progress.json`
3. **状态**：新建文件

#### 继续使用项目
1. **Assets读取**：仍从 `animal-1.json` 读取原始数据
2. **进度文件更新**：更新现有的 `animal-1_full_progress.json`
3. **状态**：更新现有文件

### 文件操作类型

| 文件类型 | 操作类型 | 频率 | 位置 |
|---------|---------|------|------|
| Assets JSON/PNG | 只读 | 每次启动 | APK内部 |
| 进度JSON | 创建/更新 | 每次填色 | 应用数据目录 |
| 预览PNG | 创建/更新 | 退出时/完成时 | 应用数据目录 |

## 性能优化

### 1. 缓存机制
```kotlin
private val jsonParseCache = mutableMapMap<String, FullProjectProgress>()
```

### 2. 异步保存
```kotlin
CoroutineScope(Dispatchers.IO).launch {
    projectSaveManager.saveProgressFast(...)
}
```

### 3. 批量写入
```kotlin
fullProgressFile.bufferedWriter().use { it.write(fullProgressJson) }
```

## 数据安全

### 1. 原始数据保护
- Assets文件只读，无法被意外修改
- 即使进度文件损坏，原始项目数据仍然安全

### 2. 进度数据备份
- 每次保存都是完整覆盖
- 包含完整的coloringData，可以完全恢复

### 3. 错误恢复
```kotlin
when (progressResult) {
    is LoadResult.Success -> {
        // 使用保存的进度
        setupProjectFast(progressData.coloringData, outlineBitmap, progressData.filledRegions)
    }
    is LoadResult.Error -> {
        // 回退到原始数据
        setupProject(originalColoringData, outlineBitmap)
    }
}
```

## 总结

1. **Assets文件**：只读，永不修改，每次从APK读取
2. **进度文件**：动态创建，持续更新，存储在应用数据目录
3. **存储策略**：快速保存（实时）+ 完整保存（特定时机）
4. **数据安全**：原始数据保护 + 完整进度备份
5. **性能优化**：缓存 + 异步 + 批量写入

这种设计确保了数据的安全性和性能的最优化。