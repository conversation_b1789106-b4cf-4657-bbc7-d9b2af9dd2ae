# 简化项目名称处理

## 设计原则

既然要重新安装不考虑兼容性，我们采用最简单的方案：

**统一使用项目ID（project_id）作为唯一标识符**

## 简化逻辑

### 之前的复杂逻辑
- 原始名称 vs 标准化名称
- 文件加载名称 vs 进度保存名称
- 复杂的名称映射规则

### 现在的简化逻辑
```kotlin
val unifiedProjectName = projectId  // 统一使用项目ID

// 所有操作都使用相同的名称
progressSaveManager.loadSavedProgress(unifiedProjectName)
projectLoadManager.loadProject(unifiedProjectName, projectSource)
getStandardizedProjectName() // 返回项目ID
```

## 文件命名要求

现在assets文件夹中的文件必须与项目ID完全匹配：

### 示例
- **项目ID**: `animal-2`
- **必需文件**: `animal-2.json`, `animal-2.png`

### 不再支持的映射
- ❌ `animal-2` → `animal2`
- ❌ `Animal 2` → `animal2`
- ❌ 复杂的名称转换

## 调试日志

简化后的日志输出：
```
=== 简化项目名称处理 ===
输入 - projectId: animal-2, projectName: Animal 2
统一使用项目ID: animal-2
用于文件加载: animal-2
用于进度保存/加载: animal-2
===============================
```

## 错误处理

如果文件不存在，会显示明确的错误信息：
```
无法找到项目: animal-2
请确保assets文件夹中存在 animal-2.json 和 animal-2.png
```

## 优势

1. **简单明确**：一个项目ID，一套文件
2. **易于维护**：没有复杂的映射逻辑
3. **易于调试**：所有操作使用相同的名称
4. **避免错误**：消除名称不匹配的可能性

## 迁移要求

重新安装时需要确保：

1. **assets文件命名**：与项目ID完全匹配
2. **项目数据**：使用项目ID作为标识符
3. **进度数据**：会重新开始（不考虑兼容性）

## 示例文件结构

```
assets/
├── animal-1.json
├── animal-1.png
├── animal-2.json
├── animal-2.png
├── animal-3.json
├── animal-3.png
└── ...
```

这个简化方案消除了所有复杂的名称处理逻辑，确保了系统的简洁性和可靠性。