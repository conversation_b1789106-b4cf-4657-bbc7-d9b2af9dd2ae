# blue.png 马赛克优化方案

## 当前问题分析

### 现有马赛克实现的性能瓶颈：
1. **实时像素计算** - `drawPixelLevelMosaic()` 逐像素绘制
2. **复杂的边界裁剪** - `calculateSimplifiedClipping()` 计算开销大
3. **动态马赛克生成** - `drawCheckerboardPattern()` 实时生成格子图案
4. **缓存失效频繁** - `mosaicCacheDirty = true` 导致重复计算

### 竞品使用预制图片的优势：
- 马赛克图案已预制，无需实时生成
- 通过图片拉伸/平铺快速覆盖区域
- GPU硬件加速的图片绘制
- 内存占用可控，性能稳定

## 🚀 blue.png 优化方案

### 方案1：图片平铺马赛克（推荐）

#### 1.1 创建马赛克图片管理器
```kotlin
class MosaicImageManager(private val context: Context) {
    private var mosaicBitmap: Bitmap? = null
    private var mosaicShader: BitmapShader? = null
    private val mosaicPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    
    init {
        loadMosaicImage()
    }
    
    private fun loadMosaicImage() {
        try {
            // 从assets加载blue.png
            val inputStream = context.assets.open("blue.png")
            mosaicBitmap = BitmapFactory.decodeStream(inputStream)
            inputStream.close()
            
            // 创建平铺着色器
            mosaicBitmap?.let { bitmap ->
                mosaicShader = BitmapShader(
                    bitmap,
                    Shader.TileMode.REPEAT,
                    Shader.TileMode.REPEAT
                )
                mosaicPaint.shader = mosaicShader
                mosaicPaint.alpha = 128 // 半透明效果
            }
            
            Log.d("MosaicImageManager", "马赛克图片加载成功: ${mosaicBitmap?.width}x${mosaicBitmap?.height}")
        } catch (e: Exception) {
            Log.e("MosaicImageManager", "加载马赛克图片失败", e)
        }
    }
    
    fun drawMosaicRegion(canvas: Canvas, region: Region) {
        val shader = mosaicShader ?: return
        
        // 使用图片着色器快速绘制马赛克
        region.pixels.forEach { pixel ->
            val x = pixel[0].toFloat()
            val y = pixel[1].toFloat()
            canvas.drawPoint(x, y, mosaicPaint)
        }
    }
    
    fun drawMosaicRect(canvas: Canvas, rect: RectF) {
        mosaicShader?.let { shader ->
            canvas.drawRect(rect, mosaicPaint)
        }
    }
}
```

#### 1.2 优化ColoringView的马赛克绘制
```kotlin
class ColoringView {
    private lateinit var mosaicImageManager: MosaicImageManager
    
    init {
        mosaicImageManager = MosaicImageManager(context)
    }
    
    /**
     * 使用预制图片的高性能马赛克绘制
     */
    private fun drawHintRegionsOptimized(canvas: Canvas) {
        if (hintRegions.isEmpty()) return
        
        // 方案A：逐区域绘制（精确但较慢）
        if (hintRegions.size <= 5) {
            hintRegions.forEach { region ->
                drawRegionMosaicWithImage(canvas, region)
            }
        } else {
            // 方案B：批量绘制（快速但可能有重叠）
            drawBatchMosaicWithImage(canvas)
        }
    }
    
    /**
     * 使用图片绘制单个区域马赛克
     */
    private fun drawRegionMosaicWithImage(canvas: Canvas, region: Region) {
        // 获取区域边界框
        val boundingBox = region.boundingBox
        if (boundingBox != null && boundingBox.size >= 4) {
            val rect = RectF(
                boundingBox[0].toFloat(),
                boundingBox[1].toFloat(),
                boundingBox[2].toFloat(),
                boundingBox[3].toFloat()
            )
            
            // 使用图片快速填充矩形区域
            mosaicImageManager.drawMosaicRect(canvas, rect)
        } else {
            // 回退到像素级绘制
            mosaicImageManager.drawMosaicRegion(canvas, region)
        }
    }
    
    /**
     * 批量绘制马赛克（最高性能）
     */
    private fun drawBatchMosaicWithImage(canvas: Canvas) {
        // 计算所有提醒区域的总边界框
        val totalBounds = calculateTotalBounds(hintRegions)
        
        // 一次性绘制大区域，然后裁剪
        if (totalBounds != null) {
            canvas.save()
            
            // 设置裁剪路径（只显示实际区域）
            val clipPath = createClipPath(hintRegions)
            canvas.clipPath(clipPath)
            
            // 绘制马赛克图片
            mosaicImageManager.drawMosaicRect(canvas, totalBounds)
            
            canvas.restore()
        }
    }
}
```

### 方案2：多层马赛克图片（进阶）

#### 2.1 准备多种马赛克图片
```
assets/
├── mosaic/
│   ├── blue.png      // 蓝色马赛克
│   ├── red.png       // 红色马赛克  
│   ├── green.png     // 绿色马赛克
│   └── pattern.png   // 通用图案
```

#### 2.2 动态颜色马赛克
```kotlin
class ColorfulMosaicManager(private val context: Context) {
    private val mosaicCache = mutableMapOf<String, Bitmap>()
    private val shaderCache = mutableMapOf<String, BitmapShader>()
    
    fun getMosaicForColor(colorHex: String): BitmapShader? {
        return shaderCache[colorHex] ?: createColoredMosaic(colorHex)
    }
    
    private fun createColoredMosaic(colorHex: String): BitmapShader? {
        try {
            // 加载基础图案
            val baseBitmap = loadBaseMosaicImage()
            
            // 应用颜色滤镜
            val coloredBitmap = applyColorFilter(baseBitmap, colorHex)
            
            // 创建着色器
            val shader = BitmapShader(
                coloredBitmap,
                Shader.TileMode.REPEAT,
                Shader.TileMode.REPEAT
            )
            
            shaderCache[colorHex] = shader
            return shader
            
        } catch (e: Exception) {
            Log.e("ColorfulMosaicManager", "创建彩色马赛克失败: $colorHex", e)
            return null
        }
    }
    
    private fun applyColorFilter(bitmap: Bitmap, colorHex: String): Bitmap {
        val color = Color.parseColor(colorHex)
        val colorFilter = PorterDuffColorFilter(color, PorterDuff.Mode.MULTIPLY)
        
        val paint = Paint()
        paint.colorFilter = colorFilter
        
        val result = Bitmap.createBitmap(bitmap.width, bitmap.height, bitmap.config)
        val canvas = Canvas(result)
        canvas.drawBitmap(bitmap, 0f, 0f, paint)
        
        return result
    }
}
```

### 方案3：预缓存马赛克区域（最优性能）

#### 3.1 项目启动时预生成马赛克
```kotlin
class MosaicPreCacheManager(private val context: Context) {
    private val regionMosaicCache = mutableMapOf<String, Bitmap>()
    
    suspend fun preCacheMosaicForProject(
        projectId: String, 
        coloringData: ColoringData
    ) = withContext(Dispatchers.IO) {
        
        val mosaicImageManager = MosaicImageManager(context)
        
        // 按颜色分组区域
        val regionsByColor = coloringData.regions.groupBy { it.colorHex }
        
        regionsByColor.forEach { (colorHex, regions) ->
            // 为每种颜色预生成马赛克bitmap
            val mosaicBitmap = createMosaicBitmapForRegions(regions, mosaicImageManager)
            val cacheKey = "${projectId}_${colorHex}"
            regionMosaicCache[cacheKey] = mosaicBitmap
        }
        
        Log.d("MosaicPreCache", "预缓存完成: ${regionMosaicCache.size}个马赛克图片")
    }
    
    fun getCachedMosaic(projectId: String, colorHex: String): Bitmap? {
        return regionMosaicCache["${projectId}_${colorHex}"]
    }
    
    private fun createMosaicBitmapForRegions(
        regions: List<Region>, 
        mosaicManager: MosaicImageManager
    ): Bitmap {
        // 计算总边界
        val bounds = calculateRegionsBounds(regions)
        
        // 创建bitmap
        val bitmap = Bitmap.createBitmap(
            bounds.width().toInt(),
            bounds.height().toInt(),
            Bitmap.Config.ARGB_8888
        )
        
        val canvas = Canvas(bitmap)
        
        // 绘制所有区域的马赛克
        regions.forEach { region ->
            mosaicManager.drawMosaicRegion(canvas, region)
        }
        
        return bitmap
    }
}
```

#### 3.2 ColoringView使用预缓存
```kotlin
class ColoringView {
    private lateinit var mosaicPreCacheManager: MosaicPreCacheManager
    
    fun setColoringDataOptimized(data: ColoringData, outlineBitmap: Bitmap, projectId: String) {
        this.coloringData = data
        this.outlineBitmap = outlineBitmap
        
        // 异步预缓存马赛克
        lifecycleScope.launch {
            mosaicPreCacheManager.preCacheMosaicForProject(projectId, data)
        }
        
        // 立即显示界面，马赛克后续加载
        post {
            setupInitialTransform()
        }
        invalidate()
    }
    
    private fun drawHintRegionsFromCache(canvas: Canvas) {
        val currentColor = currentColorHex ?: return
        val projectId = getCurrentProjectId()
        
        // 直接使用预缓存的马赛克bitmap
        val cachedMosaic = mosaicPreCacheManager.getCachedMosaic(projectId, currentColor)
        
        if (cachedMosaic != null) {
            // 超快速绘制：直接画bitmap
            canvas.drawBitmap(cachedMosaic, 0f, 0f, null)
        } else {
            // 回退到实时生成
            drawHintRegionsOptimized(canvas)
        }
    }
}
```

## 🎯 推荐实施步骤

### 第1步：立即实施图片平铺方案
1. 将blue.png放入assets目录
2. 创建MosaicImageManager
3. 替换现有的drawHintRegions方法
4. 测试性能提升

### 第2步：优化为预缓存方案
1. 实现MosaicPreCacheManager
2. 在项目加载时预生成马赛克
3. ColoringView使用缓存绘制
4. 进一步提升性能

### 第3步：扩展为多色马赛克
1. 准备多种颜色的马赛克图片
2. 实现ColorfulMosaicManager
3. 根据当前选中颜色显示对应马赛克

## 📊 预期性能提升

| 方案 | 当前耗时 | 优化后耗时 | 提升幅度 |
|------|---------|-----------|---------|
| 图片平铺 | 200-500ms | 20-50ms | 80-90% |
| 预缓存 | 200-500ms | 5-10ms | 95%+ |
| 多色马赛克 | 200-500ms | 10-20ms | 90%+ |

通过使用blue.png预制图片，马赛克绘制性能可以提升80-95%，接近竞品的流畅度。