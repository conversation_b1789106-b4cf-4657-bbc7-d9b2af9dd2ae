package com.gpower.coloringbynumber.activity;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ArgbEvaluator;
import android.animation.ObjectAnimator;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Outline;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.ColorDrawable;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.Vibrator;
import android.text.SpannableStringBuilder;
import android.text.style.ForegroundColorSpan;
import android.transition.Transition;
import android.transition.TransitionInflater;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.view.ViewStub;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.TranslateAnimation;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.activity.ComponentActivity;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.constraintlayout.core.motion.utils.TypedValues;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.core.app.ActivityCompat;
import androidx.core.app.SharedElementCallback;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.LifecycleOwnerKt;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelLazy;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStore;
import androidx.lifecycle.viewmodel.CreationExtras;
import androidx.media3.exoplayer.DefaultRenderersFactory;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.ui.PlayerView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.airbnb.lottie.LottieAnimationView;
import com.anythink.basead.mixad.f.zMxP.BrjfCofrP;
import com.base.module.tools.model_base.tools.AnimUtilsKt;
import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestBuilder;
import com.bumptech.glide.RequestManager;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.Target;
import com.bumptech.glide.signature.ObjectKey;
import com.bykv.vk.openvk.preload.geckox.b.zIl.olkfIKv;
import com.color.by.number.paint.ly.pixel.art.R;
import com.color.by.number.paint.ly.pixel.art.databinding.ActivityEditBinding;
import com.color.by.number.paint.ly.pixel.art.databinding.IncludeNetworkErrorViewBinding;
import com.color.by.number.paint.ly.pixel.art.databinding.LayoutGuessColoringTipsBinding;
import com.color.by.number.paint.ly.pixel.art.databinding.LayoutPreviewLayoutBinding;
import com.color.by.number.paint.ly.pixel.art.databinding.ViewStubFinishViewBinding;
import com.color.by.number.paint.ly.pixel.art.databinding.ViewStubHobbyTipBinding;
import com.color.by.number.paint.ly.pixel.art.databinding.ViewStubPaintToolsBinding;
import com.color.by.number.paint.ly.pixel.art.databinding.ViewStubPropsHintStartBinding;
import com.color.by.number.paint.ly.pixel.art.databinding.ViewStubResultBgBinding;
import com.color.by.number.paint.ly.pixel.art.databinding.ViewStubSurpriseColorBinding;
import com.fyber.inneractive.sdk.player.exoplayer2.wawR.fEgNBjrWsgC;
import com.google.firebase.sessions.settings.RemoteSettings;
import com.gpower.coloringbynumber.App;
import com.gpower.coloringbynumber.achievement.viewmodel.ViewModelAchievement;
import com.gpower.coloringbynumber.adapter.PaintColorListAdapter;
import com.gpower.coloringbynumber.adv.AdvLoadingDialogFragment;
import com.gpower.coloringbynumber.bean.BeanPaintColor;
import com.gpower.coloringbynumber.bean.BeanTemplateInfoDBM;
import com.gpower.coloringbynumber.bean.GoodsBoughtBean;
import com.gpower.coloringbynumber.bean.PurchaseEvent;
import com.gpower.coloringbynumber.bean.v25.V25BeanCategoryDBM;
import com.gpower.coloringbynumber.bean.v25.V25BeanResourceContentsDBM;
import com.gpower.coloringbynumber.beanrelation.BeanResourceRelationTemplateInfo;
import com.gpower.coloringbynumber.component.RewardCategory;
import com.gpower.coloringbynumber.fragment.explore.themes.ExploreThemeContentActivity;
import com.gpower.coloringbynumber.hobby.HobbyCollectionActivity;
import com.gpower.coloringbynumber.hobby.vm.HobbyCollectionViewModelV25;
import com.gpower.coloringbynumber.iap.PurchaseUtil;
import com.gpower.coloringbynumber.net.V25ServerManager;
import com.gpower.coloringbynumber.pop.PopSelectColor;
import com.gpower.coloringbynumber.pop.PopToast;
import com.gpower.coloringbynumber.sdkManager.AdsActivityManager;
import com.gpower.coloringbynumber.tools.TDEventUtils;
import com.gpower.coloringbynumber.tools.f;
import com.gpower.coloringbynumber.tools.g0;
import com.gpower.coloringbynumber.tools.q0;
import com.gpower.coloringbynumber.view.ClipImageView;
import com.gpower.coloringbynumber.view.ColorPaintedView;
import com.gpower.coloringbynumber.view.CustomAutoPaintProgressBar;
import com.gpower.coloringbynumber.view.CustomPreviewImageView;
import com.gpower.coloringbynumber.view.RecyclerViewTopAnimView;
import com.gpower.coloringbynumber.viewModel.ColorBaseViewModel;
import com.gpower.coloringbynumber.viewModel.LikeViewModel;
import com.svg.module.module_svg.svg.SvgColorInfo;
import com.tools.color.layout.info.data.ColorInformation;
import com.tp.common.Constants;
import com.unity3d.services.UnityAdsConstants;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import kotlin.Lazy;
import kotlin.Pair;
import kotlin.Result;
import kotlin.Triple;
import kotlin.Unit;
import kotlin.a0;
import kotlin.coroutines.Continuation;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.functions.Function2;
import kotlin.jvm.functions.Function3;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.MutablePropertyReference1Impl;
import kotlin.jvm.internal.e0;
import kotlin.jvm.internal.u;
import kotlin.reflect.KProperty;
import kotlin.s0;
import kotlin.v;
import kotlinx.coroutines.y1;
import kotlinx.coroutines.z0;
import org.greenrobot.eventbus.ThreadMode;

/* loaded from: classes2.dex */
public abstract class ColoringActivity extends AbsProductConActivity implements View.OnClickListener {
    static final /* synthetic */ KProperty<Object>[] $$delegatedProperties = {e0.k(new MutablePropertyReference1Impl(ColoringActivity.class, "mAutoPaintFindNum", "getMAutoPaintFindNum()I", 0)), e0.k(new MutablePropertyReference1Impl(ColoringActivity.class, "mColoringPropsFindNum", "getMColoringPropsFindNum()I", 0))};
    private String abTestResultCollection;
    private String abTestSubscriptionPage;
    private String abTestSurpriseColor;
    private String abTestTipProp;
    private String abTestUsAdVert;
    private final long autoPaintTime = 10000;
    private boolean bannerShow;
    private int blockNumber;
    private String categoryId = "";
    private String categoryName = "";
    private boolean clickExit;
    private String clickLocation;
    private int colorNumber;
    private int countIndex;
    private int curPaintStage;
    private boolean curStatus;
    private int drawDiffLevel;
    private boolean enterShow = true;
    private float entityHeight = 500.0f;
    private float entityWidth = 500.0f;
    private boolean eventReportProgress50;
    private long exitDelay;
    private ArrayList<String> fileMimeString = new ArrayList<>();
    private ArrayList<String> fileNameStrings = new ArrayList<>();
    private boolean firstWeekDiscountSubScribed;
    private int hobbyMarginTop;
    private boolean isBoughtCollectPackage;
    private boolean isFinished;
    private boolean isHobbyCollection;
    private boolean isLike;
    private boolean isRewardLoaded;
    private boolean isShareVideo;
    private int lineNumber;
    private boolean loading = true;
    private final Lazy mAdapterListener$delegate = a0.c(new Function0() { // from class: com.gpower.coloringbynumber.activity.h0
        @Override // kotlin.jvm.functions.Function0
        public final Object invoke() {
            return ColoringActivity.mAdapterListener_delegate$lambda$3(ColoringActivity.this);
        }
    });
    private String mAdvPosition = "enter";
    private AnimatorSet mAnim;
    private AnimatorSet mAnimSet;
    private final Lazy mAspectRatio$delegate = a0.c(new Function0() { // from class: com.gpower.coloringbynumber.activity.m0
        @Override // kotlin.jvm.functions.Function0
        public final Object invoke() {
            return Float.valueOf(ColoringActivity.mAspectRatio_delegate$lambda$8(ColoringActivity.this));
        }
    });
    private final gc.f mAutoPaintFindNum$delegate;
    private ActivityEditBinding mBinding;
    private final List<String> mCalendarIdList = new ArrayList();
    private final List<String> mCollectIdList = new ArrayList();
    private final Lazy mCollectionViewModel$delegate = new ViewModelLazy(e0.d(HobbyCollectionViewModelV25.class), new Function0<ViewModelStore>(this) { // from class: com.gpower.coloringbynumber.activity.ColoringActivity$special$$inlined$viewModels$default$8
        final /* synthetic */ ComponentActivity $this_viewModels;

        {
            this.$this_viewModels = r1;
        }

        @Override // kotlin.jvm.functions.Function0
        public final ViewModelStore invoke() {
            return this.$this_viewModels.getViewModelStore();
        }
    }, new Function0<ViewModelProvider.Factory>(this) { // from class: com.gpower.coloringbynumber.activity.ColoringActivity$special$$inlined$viewModels$default$7
        final /* synthetic */ ComponentActivity $this_viewModels;

        {
            this.$this_viewModels = r1;
        }

        @Override // kotlin.jvm.functions.Function0
        public final ViewModelProvider.Factory invoke() {
            return this.$this_viewModels.getDefaultViewModelProviderFactory();
        }
    }, new Function0<CreationExtras>(null, this) { // from class: com.gpower.coloringbynumber.activity.ColoringActivity$special$$inlined$viewModels$default$9
        final /* synthetic */ Function0 $extrasProducer;
        final /* synthetic */ ComponentActivity $this_viewModels;

        {
            this.$extrasProducer = r1;
            this.$this_viewModels = r2;
        }

        @Override // kotlin.jvm.functions.Function0
        public final CreationExtras invoke() {
            CreationExtras creationExtras;
            Function0 function0 = this.$extrasProducer;
            return (function0 == null || (creationExtras = (CreationExtras) function0.invoke()) == null) ? this.$this_viewModels.getDefaultViewModelCreationExtras() : creationExtras;
        }
    });
    private final int[] mColorAnim;
    private LinearLayoutManager mColorLayoutManager;
    private final Lazy mColorListAdapter$delegate = a0.c(new Function0() { // from class: com.gpower.coloringbynumber.activity.i0
        @Override // kotlin.jvm.functions.Function0
        public final Object invoke() {
            return ColoringActivity.mColorListAdapter_delegate$lambda$4(ColoringActivity.this);
        }
    });
    private final l0.b mColorListener;
    private final Lazy mColorViewModel$delegate = new ViewModelLazy(e0.d(ColorBaseViewModel.class), new Function0<ViewModelStore>(this) { // from class: com.gpower.coloringbynumber.activity.ColoringActivity$special$$inlined$viewModels$default$5
        final /* synthetic */ ComponentActivity $this_viewModels;

        {
            this.$this_viewModels = r1;
        }

        @Override // kotlin.jvm.functions.Function0
        public final ViewModelStore invoke() {
            return this.$this_viewModels.getViewModelStore();
        }
    }, new Function0<ViewModelProvider.Factory>(this) { // from class: com.gpower.coloringbynumber.activity.ColoringActivity$special$$inlined$viewModels$default$4
        final /* synthetic */ ComponentActivity $this_viewModels;

        {
            this.$this_viewModels = r1;
        }

        @Override // kotlin.jvm.functions.Function0
        public final ViewModelProvider.Factory invoke() {
            return this.$this_viewModels.getDefaultViewModelProviderFactory();
        }
    }, new Function0<CreationExtras>(null, this) { // from class: com.gpower.coloringbynumber.activity.ColoringActivity$special$$inlined$viewModels$default$6
        final /* synthetic */ Function0 $extrasProducer;
        final /* synthetic */ ComponentActivity $this_viewModels;

        {
            this.$extrasProducer = r1;
            this.$this_viewModels = r2;
        }

        @Override // kotlin.jvm.functions.Function0
        public final CreationExtras invoke() {
            CreationExtras creationExtras;
            Function0 function0 = this.$extrasProducer;
            return (function0 == null || (creationExtras = (CreationExtras) function0.invoke()) == null) ? this.$this_viewModels.getDefaultViewModelCreationExtras() : creationExtras;
        }
    });
    private final gc.f mColoringPropsFindNum$delegate;
    private final Lazy mDataId$delegate = a0.c(new Function0() { // from class: com.gpower.coloringbynumber.activity.a0
        @Override // kotlin.jvm.functions.Function0
        public final Object invoke() {
            return ColoringActivity.mDataId_delegate$lambda$13(ColoringActivity.this);
        }
    });
    private final Lazy mDialogRemoveWaterMark$delegate = a0.c(new Function0() { // from class: com.gpower.coloringbynumber.activity.x
        @Override // kotlin.jvm.functions.Function0
        public final Object invoke() {
            return ColoringActivity.mDialogRemoveWaterMark_delegate$lambda$0(ColoringActivity.this);
        }
    });
    private ViewStubFinishViewBinding mFinishResultBinding;
    private float mFinishTopPadding = 140.0f;
    private f.a mFrameAnim;
    private final Lazy mGuessCover$delegate = a0.c(new Function0() { // from class: com.gpower.coloringbynumber.activity.o0
        @Override // kotlin.jvm.functions.Function0
        public final Object invoke() {
            return Integer.valueOf(ColoringActivity.mGuessCover_delegate$lambda$10(ColoringActivity.this));
        }
    });
    private ViewStubHobbyTipBinding mHobbyIconBinding;
    private final Lazy mImageUrl$delegate = a0.c(new Function0() { // from class: com.gpower.coloringbynumber.activity.k0
        @Override // kotlin.jvm.functions.Function0
        public final Object invoke() {
            return ColoringActivity.mImageUrl_delegate$lambda$6(ColoringActivity.this);
        }
    });
    private boolean mIsGenerateVideo;
    private boolean mIsSaveSuccess;
    private boolean mIsShowRemind;
    private final Lazy mLikeViewModel$delegate = new ViewModelLazy(e0.d(LikeViewModel.class), new Function0<ViewModelStore>(this) { // from class: com.gpower.coloringbynumber.activity.ColoringActivity$special$$inlined$viewModels$default$2
        final /* synthetic */ ComponentActivity $this_viewModels;

        {
            this.$this_viewModels = r1;
        }

        @Override // kotlin.jvm.functions.Function0
        public final ViewModelStore invoke() {
            return this.$this_viewModels.getViewModelStore();
        }
    }, new Function0<ViewModelProvider.Factory>(this) { // from class: com.gpower.coloringbynumber.activity.ColoringActivity$special$$inlined$viewModels$default$1
        final /* synthetic */ ComponentActivity $this_viewModels;

        {
            this.$this_viewModels = r1;
        }

        @Override // kotlin.jvm.functions.Function0
        public final ViewModelProvider.Factory invoke() {
            return this.$this_viewModels.getDefaultViewModelProviderFactory();
        }
    }, new Function0<CreationExtras>(null, this) { // from class: com.gpower.coloringbynumber.activity.ColoringActivity$special$$inlined$viewModels$default$3
        final /* synthetic */ Function0 $extrasProducer;
        final /* synthetic */ ComponentActivity $this_viewModels;

        {
            this.$extrasProducer = r1;
            this.$this_viewModels = r2;
        }

        @Override // kotlin.jvm.functions.Function0
        public final CreationExtras invoke() {
            CreationExtras creationExtras;
            Function0 function0 = this.$extrasProducer;
            return (function0 == null || (creationExtras = (CreationExtras) function0.invoke()) == null) ? this.$this_viewModels.getDefaultViewModelCreationExtras() : creationExtras;
        }
    });
    private boolean mLoadingAnimResult;
    private final int[] mLongColorAnim;
    private final Lazy mObjectKey$delegate = a0.c(new Function0() { // from class: com.gpower.coloringbynumber.activity.n0
        @Override // kotlin.jvm.functions.Function0
        public final Object invoke() {
            return ColoringActivity.mObjectKey_delegate$lambda$9(ColoringActivity.this);
        }
    });
    private ViewStubPaintToolsBinding mPaintToolsBinding;
    private int mPathPaintCount;
    private int mPathTotalCount;
    private final ActivityResultLauncher<String[]> mPermissionLauncher = registerForActivityResult(new ActivityResultContracts.RequestMultiplePermissions(), new ActivityResultCallback() { // from class: com.gpower.coloringbynumber.activity.d0
        @Override // androidx.activity.result.ActivityResultCallback
        public final void onActivityResult(Object obj) {
            ColoringActivity.mPermissionLauncher$lambda$18(ColoringActivity.this, (Map) obj);
        }
    });
    private ExoPlayer mPlayer;
    private int mPlayerStatus;
    private final Lazy mPopSub$delegate = a0.c(new Function0() { // from class: com.gpower.coloringbynumber.activity.g0
        @Override // kotlin.jvm.functions.Function0
        public final Object invoke() {
            return ColoringActivity.mPopSub_delegate$lambda$2(ColoringActivity.this);
        }
    });
    private PopToast mPopToast;
    private final Lazy mPopupClaimProp$delegate;
    private com.gpower.coloringbynumber.pop.s mPopupDownload;
    private final Lazy mPopupPropsGift$delegate;
    private final Lazy mPopupSelectColor$delegate = a0.c(new Function0() { // from class: com.gpower.coloringbynumber.activity.c0
        @Override // kotlin.jvm.functions.Function0
        public final Object invoke() {
            return ColoringActivity.mPopupSelectColor_delegate$lambda$15(ColoringActivity.this);
        }
    });
    private RectF mPreRectF = new RectF();
    private Pair<Float, Float> mPropsFindPair;
    private BeanResourceRelationTemplateInfo mRelationBean;
    private ViewStubResultBgBinding mResultBgBinding;
    private RewardCategory mRewardCategory = RewardCategory.NONE;
    private final Lazy mRewardUnLock$delegate = a0.c(new Function0() { // from class: com.gpower.coloringbynumber.activity.y
        @Override // kotlin.jvm.functions.Function0
        public final Object invoke() {
            return Boolean.valueOf(ColoringActivity.mRewardUnLock_delegate$lambda$11(ColoringActivity.this));
        }
    });
    private Runnable mSaveRunnable;
    private Runnable mSendRefreshRunnable;
    private Runnable mShareRunnable;
    private final Lazy mShowAlpha$delegate = a0.c(new Function0() { // from class: com.gpower.coloringbynumber.activity.z
        @Override // kotlin.jvm.functions.Function0
        public final Object invoke() {
            return Boolean.valueOf(ColoringActivity.mShowAlpha_delegate$lambda$12(ColoringActivity.this));
        }
    });
    private final Lazy mShowTrans$delegate = a0.c(new Function0() { // from class: com.gpower.coloringbynumber.activity.l0
        @Override // kotlin.jvm.functions.Function0
        public final Object invoke() {
            return Boolean.valueOf(ColoringActivity.mShowTrans_delegate$lambda$7(ColoringActivity.this));
        }
    });
    private g0 mSingleMediaScanner;
    private long mStartPaintTime;
    private Runnable mStartRunnable;
    private ViewStubSurpriseColorBinding mSurpriseColoringBinding;
    private String mSvgName = "";
    private String mSvgNameVersion = "";
    private final Lazy mSvgPath$delegate = a0.c(new Function0() { // from class: com.gpower.coloringbynumber.activity.j0
        @Override // kotlin.jvm.functions.Function0
        public final Object invoke() {
            return ColoringActivity.mSvgPath_delegate$lambda$5(ColoringActivity.this);
        }
    });
    private int mThisTimesIntoCount;
    private final Lazy mV25Data$delegate = a0.c(new Function0() { // from class: com.gpower.coloringbynumber.activity.b0
        @Override // kotlin.jvm.functions.Function0
        public final Object invoke() {
            return Boolean.valueOf(ColoringActivity.mV25Data_delegate$lambda$14(ColoringActivity.this));
        }
    });
    private Vibrator mVibrator;
    private ViewStubPropsHintStartBinding mViewPropHintBinding;
    private LayoutGuessColoringTipsBinding mVsGuessTipsBinding;
    private long openEditTimeDuration;
    private Long openEditTimeStamp;
    private boolean propsUnUse;
    private boolean pvBeginEventReport;
    private boolean refreshFlowResult;
    private boolean remoteBannerControl;
    private com.gpower.coloringbynumber.tools.e0 shareToAction;
    private boolean showAnimLoading;
    private long showPropGitDuration;
    private boolean showTd;
    private MediaPlayer soundMediaPlayer;
    private boolean startPainted;
    private boolean swipeDrawAvailable;

    /* loaded from: classes2.dex */
    public /* synthetic */ class a {
        public static final /* synthetic */ int[] $EnumSwitchMapping$0;

        static {
            int[] iArr = new int[RewardCategory.values().length];
            try {
                iArr[RewardCategory.EDIT_COLOR_HINT_TOP.ordinal()] = 1;
            } catch (NoSuchFieldError unused) {
            }
            try {
                iArr[RewardCategory.EDIT_COLOR_AUTO_TOP.ordinal()] = 2;
            } catch (NoSuchFieldError unused2) {
            }
            try {
                iArr[RewardCategory.EDIT_COLOR_HINT_RIGHT.ordinal()] = 3;
            } catch (NoSuchFieldError unused3) {
            }
            try {
                iArr[RewardCategory.WATER_MARK.ordinal()] = 4;
            } catch (NoSuchFieldError unused4) {
            }
            try {
                iArr[RewardCategory.EDIT_COLOR_PROP_GIFT.ordinal()] = 5;
            } catch (NoSuchFieldError unused5) {
            }
            try {
                iArr[RewardCategory.EDIT_COLOR_PROP_PALETTE.ordinal()] = 6;
            } catch (NoSuchFieldError unused6) {
            }
            $EnumSwitchMapping$0 = iArr;
        }
    }

    /* loaded from: classes2.dex */
    public static final class b implements Animator.AnimatorListener {

        /* renamed from: n  reason: collision with root package name */
        public final /* synthetic */ ColoringActivity f32064n;

        public b(ColoringActivity coloringActivity) {
            this.f32064n = coloringActivity;
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationCancel(Animator animator) {
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationEnd(Animator animator) {
            this.f32064n.showAnimLoading = false;
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationRepeat(Animator animator) {
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationStart(Animator animator) {
        }
    }

    /* loaded from: classes2.dex */
    public static final class c implements Animator.AnimatorListener {
        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationCancel(Animator animator) {
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationEnd(Animator animator) {
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationRepeat(Animator animator) {
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationStart(Animator animator) {
        }
    }

    /* loaded from: classes2.dex */
    public static final class d implements g2.a {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ ColoringActivity f32065a;

        public d(ColoringActivity coloringActivity) {
            this.f32065a = coloringActivity;
        }

        public static final void l(ColoringActivity coloringActivity, BeanPaintColor beanPaintColor) {
            RecyclerViewTopAnimView recyclerViewTopAnimView;
            int deletePaintInfo = coloringActivity.getMColorListAdapter().deletePaintInfo(beanPaintColor);
            ViewStubPaintToolsBinding mPaintToolsBinding = coloringActivity.getMPaintToolsBinding();
            if (!(mPaintToolsBinding == null || (recyclerViewTopAnimView = mPaintToolsBinding.editColoringAnimView) == null)) {
                RecyclerViewTopAnimView.b(recyclerViewTopAnimView, 0, 0.0f, 2, null);
            }
            beanPaintColor.setFinishPainted(true);
            coloringActivity.getMColorListAdapter().getMList().add(beanPaintColor);
            if (com.gpower.coloringbynumber.spf.c.f33083b.B()) {
                if (coloringActivity.getMColorListAdapter().getMList().get(deletePaintInfo).getFinishPainted()) {
                    if (coloringActivity.getMColorListAdapter().getMList().get(0).getFinishPainted()) {
                        deletePaintInfo = -1;
                    } else {
                        coloringActivity.refreshRecyclerViewPosition(0);
                    }
                }
                coloringActivity.startPaintFinishAnim(deletePaintInfo);
            }
            deletePaintInfo = 0;
            coloringActivity.startPaintFinishAnim(deletePaintInfo);
        }

        @Override // g2.a
        public void a(BeanPaintColor beanPaintColor) {
            Intrinsics.checkNotNullParameter(beanPaintColor, "curPosition");
            new Handler(Looper.getMainLooper()).post(new f2(this.f32065a, beanPaintColor));
        }

        @Override // g2.a
        public void b(BeanPaintColor beanPaintColor) {
        }

        @Override // g2.a
        public void c(float f10) {
            ColorPaintedView colorPaintedView;
            ViewStubPaintToolsBinding mPaintToolsBinding = this.f32065a.getMPaintToolsBinding();
            if (mPaintToolsBinding != null && (colorPaintedView = mPaintToolsBinding.editSingleAnimView) != null) {
                colorPaintedView.setMProgress(f10);
            }
        }

        @Override // g2.a
        public void d() {
            this.f32065a.propsUnUse = false;
        }

        @Override // g2.a
        public void e(int i10) {
            this.f32065a.startShowGiftAnim(i10);
        }

        @Override // g2.a
        public void f(float f10, BeanPaintColor beanPaintColor) {
            Intrinsics.checkNotNullParameter(beanPaintColor, "mBeanColor");
            this.f32065a.refreshUnSelectColorAnim(f10, beanPaintColor);
        }

        @Override // g2.a
        public void g(BeanPaintColor beanPaintColor) {
            if (this.f32065a.getMPopupSelectColor().isShowing()) {
                this.f32065a.getMPopupSelectColor().dismiss();
            }
            this.f32065a.propsUnUse = false;
            int size = this.f32065a.getMColorListAdapter().getMList().size();
            int selectPosition = this.f32065a.getMColorListAdapter().getSelectPosition();
            if (selectPosition >= 0 && selectPosition < size) {
                if (this.f32065a.getMColorListAdapter().getMList().get(this.f32065a.getMColorListAdapter().getSelectPosition()).getShowGift()) {
                    this.f32065a.createSurpriseColorBinding();
                }
                ColoringActivity coloringActivity = this.f32065a;
                coloringActivity.dispatchSelectedColorId(coloringActivity.getMColorListAdapter().getMList().get(this.f32065a.getMColorListAdapter().getSelectPosition()).curColorIndex(), true);
            }
        }

        @Override // g2.a
        public void h(float f10, BeanPaintColor beanPaintColor) {
            Intrinsics.checkNotNullParameter(beanPaintColor, "mBeanColor");
            this.f32065a.refreshSelectColorAnim(f10);
        }

        @Override // g2.a
        public void i(BeanPaintColor beanPaintColor) {
            ColorPaintedView colorPaintedView;
            Integer num;
            ColorPaintedView colorPaintedView2;
            RecyclerViewTopAnimView recyclerViewTopAnimView;
            ViewStubPaintToolsBinding mPaintToolsBinding = this.f32065a.getMPaintToolsBinding();
            if (!(mPaintToolsBinding == null || (recyclerViewTopAnimView = mPaintToolsBinding.editColoringAnimView) == null)) {
                recyclerViewTopAnimView.c(false);
            }
            ViewStubPaintToolsBinding mPaintToolsBinding2 = this.f32065a.getMPaintToolsBinding();
            if (!(mPaintToolsBinding2 == null || (colorPaintedView2 = mPaintToolsBinding2.editSingleAnimView) == null)) {
                colorPaintedView2.setVisibility(0);
            }
            ViewStubPaintToolsBinding mPaintToolsBinding3 = this.f32065a.getMPaintToolsBinding();
            if (mPaintToolsBinding3 != null && (colorPaintedView = mPaintToolsBinding3.editSingleAnimView) != null) {
                if (beanPaintColor != null) {
                    num = Integer.valueOf(beanPaintColor.curColorInfo());
                } else {
                    num = null;
                }
                colorPaintedView.setCurColor(num);
            }
        }

        @Override // g2.a
        public void j(int i10) {
            this.f32065a.refreshRecyclerViewPosition(i10);
        }
    }

    /* loaded from: classes2.dex */
    public static final class e implements l0.b {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ ColoringActivity f32066a;

        public e(ColoringActivity coloringActivity) {
            this.f32066a = coloringActivity;
        }

        public static final void l(ColoringActivity coloringActivity, RectF rectF) {
            Runnable runnable;
            coloringActivity.mStartRunnable = new g2(coloringActivity, rectF);
            if (coloringActivity.mLoadingAnimResult && (runnable = coloringActivity.mStartRunnable) != null) {
                runnable.run();
            }
        }

        public static final void m(ColoringActivity coloringActivity, RectF rectF) {
            if (coloringActivity.mLoadingAnimResult) {
                coloringActivity.pathProViewInitFinish(rectF);
                coloringActivity.pvBeginTemplateParse();
                coloringActivity.mStartRunnable = null;
            }
        }

        @Override // l0.b
        public void a() {
            this.f32066a.showSelectColor();
        }

        @Override // l0.b
        public void b() {
            this.f32066a.showFinishFlag();
        }

        @Override // l0.b
        public void c(RectF rectF) {
            Intrinsics.checkNotNullParameter(rectF, "mRectF");
            ColoringActivity coloringActivity = this.f32066a;
            coloringActivity.runOnUiThread(new h2(coloringActivity, rectF));
        }

        @Override // l0.b
        public void d(boolean z10) {
            AppCompatImageView appCompatImageView;
            int i10;
            AppCompatImageView appCompatImageView2;
            if (this.f32066a.isFinished()) {
                ViewStubPaintToolsBinding mPaintToolsBinding = this.f32066a.getMPaintToolsBinding();
                if (mPaintToolsBinding != null && (appCompatImageView2 = mPaintToolsBinding.btnScale) != null) {
                    appCompatImageView2.setVisibility(8);
                    return;
                }
                return;
            }
            ViewStubPaintToolsBinding mPaintToolsBinding2 = this.f32066a.getMPaintToolsBinding();
            if (mPaintToolsBinding2 != null && (appCompatImageView = mPaintToolsBinding2.btnScale) != null) {
                if (z10) {
                    i10 = 0;
                } else {
                    i10 = 4;
                }
                appCompatImageView.setVisibility(i10);
            }
        }

        @Override // l0.b
        public void e(int i10) {
            this.f32066a.onLongPress(i10);
        }

        @Override // l0.b
        public void f(float f10) {
            BeanTemplateInfoDBM beanTemplateInfo;
            BeanTemplateInfoDBM beanTemplateInfo2;
            BeanResourceRelationTemplateInfo mRelationBean;
            BeanTemplateInfoDBM beanTemplateInfo3;
            this.f32066a.checkPathCount(Float.valueOf(f10));
            BeanResourceRelationTemplateInfo mRelationBean2 = this.f32066a.getMRelationBean();
            if (!(mRelationBean2 == null || (beanTemplateInfo2 = mRelationBean2.getBeanTemplateInfo()) == null || beanTemplateInfo2.isPainted() != 0 || (mRelationBean = this.f32066a.getMRelationBean()) == null || (beanTemplateInfo3 = mRelationBean.getBeanTemplateInfo()) == null)) {
                beanTemplateInfo3.setPainted(1);
            }
            BeanResourceRelationTemplateInfo mRelationBean3 = this.f32066a.getMRelationBean();
            if (mRelationBean3 != null && (beanTemplateInfo = mRelationBean3.getBeanTemplateInfo()) != null) {
                beanTemplateInfo.setPaintProgress(f10);
            }
        }

        @Override // l0.b
        public void g(boolean z10) {
            this.f32066a.enterResultView(z10);
        }

        @Override // l0.b
        public void h(RectF rectF) {
            Intrinsics.checkNotNullParameter(rectF, "mRectF");
            this.f32066a.showResultAnim(rectF);
        }

        @Override // l0.b
        public void i(RectF rectF) {
            Intrinsics.checkNotNullParameter(rectF, "mRectF");
            this.f32066a.resultAnimFinish(rectF);
        }
    }

    /* loaded from: classes2.dex */
    public static final class f implements g2.l {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ ColoringActivity f32067a;

        public f(ColoringActivity coloringActivity) {
            this.f32067a = coloringActivity;
        }

        @Override // g2.l
        public void a() {
            this.f32067a.showRemoveWaterMarkActivity();
        }

        @Override // g2.l
        public void b() {
            PayActivity.Companion.a(this.f32067a, k2.g.f96907s);
        }
    }

    /* loaded from: classes2.dex */
    public static final class g implements g2.k {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ ColoringActivity f32068a;

        public g(ColoringActivity coloringActivity) {
            this.f32068a = coloringActivity;
        }

        @Override // g2.k
        public void a(int i10) {
            if (i10 == 1) {
                this.f32068a.setMRewardCategory(RewardCategory.EDIT_COLOR_PROP_GIFT);
                ColoringActivity.showRewardVideo$default(this.f32068a, k2.g.A, false, 2, null);
            } else if (i10 == 2) {
                this.f32068a.setMRewardCategory(RewardCategory.EDIT_COLOR_PROP_PALETTE);
                ColoringActivity.showRewardVideo$default(this.f32068a, k2.g.f96868f, false, 2, null);
            }
        }

        @Override // g2.k
        public void b() {
            Toast.makeText(this.f32068a, R.string.claim_fail, 1).show();
            this.f32068a.propGiftRestartCalculationTime(3);
        }
    }

    /* loaded from: classes2.dex */
    public static final class h implements g2.k {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ ColoringActivity f32069a;

        public h(ColoringActivity coloringActivity) {
            this.f32069a = coloringActivity;
        }

        @Override // g2.k
        public void a(int i10) {
            ConstraintLayout root;
            ActivityEditBinding mBinding = this.f32069a.getMBinding();
            if (mBinding != null && (root = mBinding.getRoot()) != null) {
                this.f32069a.getMPopupClaimProp().f(root, "gift_pack");
            }
        }

        @Override // g2.k
        public void b() {
            this.f32069a.propGiftRestartCalculationTime(2);
        }
    }

    /* loaded from: classes2.dex */
    public static final class i implements Animator.AnimatorListener {

        /* renamed from: n  reason: collision with root package name */
        public final /* synthetic */ ColoringActivity f32070n;

        /* renamed from: u  reason: collision with root package name */
        public final /* synthetic */ ObjectAnimator f32071u;

        public i(ColoringActivity coloringActivity, ObjectAnimator objectAnimator) {
            this.f32070n = coloringActivity;
            this.f32071u = objectAnimator;
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationCancel(Animator animator) {
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationEnd(Animator animator) {
            ConstraintLayout constraintLayout;
            TextView textView;
            ConstraintLayout constraintLayout2;
            TextView textView2;
            ConstraintLayout constraintLayout3;
            TextView textView3;
            this.f32070n.dispatchInitAnimFinish();
            this.f32070n.showBottomToolsLayoutAnim();
            ViewStubPaintToolsBinding mPaintToolsBinding = this.f32070n.getMPaintToolsBinding();
            if (!(mPaintToolsBinding == null || (constraintLayout3 = mPaintToolsBinding.paintToolsRootLayout) == null)) {
                ViewStubPaintToolsBinding mPaintToolsBinding2 = this.f32070n.getMPaintToolsBinding();
                if (mPaintToolsBinding2 != null) {
                    textView3 = mPaintToolsBinding2.testAllBlockNumber;
                } else {
                    textView3 = null;
                }
                constraintLayout3.removeView(textView3);
            }
            ViewStubPaintToolsBinding mPaintToolsBinding3 = this.f32070n.getMPaintToolsBinding();
            if (!(mPaintToolsBinding3 == null || (constraintLayout2 = mPaintToolsBinding3.paintToolsRootLayout) == null)) {
                ViewStubPaintToolsBinding mPaintToolsBinding4 = this.f32070n.getMPaintToolsBinding();
                if (mPaintToolsBinding4 != null) {
                    textView2 = mPaintToolsBinding4.testAutoPaint;
                } else {
                    textView2 = null;
                }
                constraintLayout2.removeView(textView2);
            }
            ViewStubPaintToolsBinding mPaintToolsBinding5 = this.f32070n.getMPaintToolsBinding();
            if (!(mPaintToolsBinding5 == null || (constraintLayout = mPaintToolsBinding5.paintToolsRootLayout) == null)) {
                ViewStubPaintToolsBinding mPaintToolsBinding6 = this.f32070n.getMPaintToolsBinding();
                if (mPaintToolsBinding6 != null) {
                    textView = mPaintToolsBinding6.testAutoPaint2;
                } else {
                    textView = null;
                }
                constraintLayout.removeView(textView);
            }
            if (!this.f32070n.clickExit) {
                this.f32070n.mAnimSet = null;
                ColoringActivity coloringActivity = this.f32070n;
                AnimatorSet animatorSet = new AnimatorSet();
                animatorSet.play(this.f32071u);
                animatorSet.setDuration(500L);
                animatorSet.addListener(new j(this.f32070n));
                animatorSet.start();
                coloringActivity.mAnimSet = animatorSet;
            }
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationRepeat(Animator animator) {
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationStart(Animator animator) {
        }
    }

    /* loaded from: classes2.dex */
    public static final class j implements Animator.AnimatorListener {

        /* renamed from: n  reason: collision with root package name */
        public final /* synthetic */ ColoringActivity f32072n;

        public j(ColoringActivity coloringActivity) {
            this.f32072n = coloringActivity;
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationCancel(Animator animator) {
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationEnd(Animator animator) {
            ViewStubPaintToolsBinding mPaintToolsBinding;
            ConstraintLayout constraintLayout;
            ConstraintLayout constraintLayout2;
            ConstraintLayout constraintLayout3;
            IncludeNetworkErrorViewBinding includeNetworkErrorViewBinding;
            ConstraintLayout constraintLayout4;
            ConstraintLayout constraintLayout5;
            LayoutPreviewLayoutBinding layoutPreviewLayoutBinding;
            LayoutPreviewLayoutBinding layoutPreviewLayoutBinding2;
            ConstraintLayout constraintLayout6;
            if (!this.f32072n.clickExit) {
                ActivityEditBinding mBinding = this.f32072n.getMBinding();
                if (!(mBinding == null || (layoutPreviewLayoutBinding2 = mBinding.editIncludePreviewLayout) == null || (constraintLayout6 = layoutPreviewLayoutBinding2.previewRootLayout) == null)) {
                    constraintLayout6.setVisibility(8);
                }
                ActivityEditBinding mBinding2 = this.f32072n.getMBinding();
                if (!(mBinding2 == null || (constraintLayout4 = mBinding2.editTotalLayout) == null)) {
                    ActivityEditBinding mBinding3 = this.f32072n.getMBinding();
                    if (mBinding3 == null || (layoutPreviewLayoutBinding = mBinding3.editIncludePreviewLayout) == null) {
                        constraintLayout5 = null;
                    } else {
                        constraintLayout5 = layoutPreviewLayoutBinding.previewRootLayout;
                    }
                    constraintLayout4.removeView(constraintLayout5);
                }
                ActivityEditBinding mBinding4 = this.f32072n.getMBinding();
                if (!(mBinding4 == null || (constraintLayout2 = mBinding4.editTotalLayout) == null)) {
                    ActivityEditBinding mBinding5 = this.f32072n.getMBinding();
                    if (mBinding5 == null || (includeNetworkErrorViewBinding = mBinding5.editIncludeErrorLayout) == null) {
                        constraintLayout3 = null;
                    } else {
                        constraintLayout3 = includeNetworkErrorViewBinding.errorView;
                    }
                    constraintLayout2.removeView(constraintLayout3);
                }
                this.f32072n.setLoading(false);
                this.f32072n.lastOpenEditTime();
                com.gpower.coloringbynumber.spf.c cVar = com.gpower.coloringbynumber.spf.c.f33083b;
                if (cVar.A0() && (mPaintToolsBinding = this.f32072n.getMPaintToolsBinding()) != null && (constraintLayout = mPaintToolsBinding.activityEditPathBottomLayout) != null && this.f32072n.curStatus) {
                    this.f32072n.getMPopupSelectColor().a(constraintLayout);
                    cVar.j2(false);
                    this.f32072n.mHandler.sendEmptyMessageDelayed(311, 3000);
                }
                if (!cVar.W0() && !cVar.w0()) {
                    this.f32072n.propGiftRestartCalculationTime(7);
                }
                this.f32072n.initOrShowBannerAd();
                this.f32072n.setCurPaintStage(2);
                this.f32072n.mAnimSet = null;
            }
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationRepeat(Animator animator) {
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationStart(Animator animator) {
        }
    }

    /* loaded from: classes2.dex */
    public static final class k implements Observer, u {

        /* renamed from: n  reason: collision with root package name */
        public final /* synthetic */ Function1 f32073n;

        public k(Function1 function1) {
            Intrinsics.checkNotNullParameter(function1, "function");
            this.f32073n = function1;
        }

        public final boolean equals(Object obj) {
            if (!(obj instanceof Observer) || !(obj instanceof u)) {
                return false;
            }
            return Intrinsics.g(getFunctionDelegate(), ((u) obj).getFunctionDelegate());
        }

        @Override // kotlin.jvm.internal.u
        public final v<?> getFunctionDelegate() {
            return this.f32073n;
        }

        public final int hashCode() {
            return getFunctionDelegate().hashCode();
        }

        @Override // androidx.lifecycle.Observer
        public final /* synthetic */ void onChanged(Object obj) {
            this.f32073n.invoke(obj);
        }
    }

    /* loaded from: classes2.dex */
    public static final class l implements Animator.AnimatorListener {

        /* renamed from: n  reason: collision with root package name */
        public final /* synthetic */ Function0 f32074n;

        public l(Function0 function0) {
            this.f32074n = function0;
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationCancel(Animator animator) {
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationEnd(Animator animator) {
            this.f32074n.invoke();
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationRepeat(Animator animator) {
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationStart(Animator animator) {
        }
    }

    /* loaded from: classes2.dex */
    public static final class m implements Animator.AnimatorListener {

        /* renamed from: n  reason: collision with root package name */
        public final /* synthetic */ ViewStubFinishViewBinding f32075n;

        /* renamed from: u  reason: collision with root package name */
        public final /* synthetic */ ColoringActivity f32076u;

        public m(ViewStubFinishViewBinding viewStubFinishViewBinding, ColoringActivity coloringActivity) {
            this.f32075n = viewStubFinishViewBinding;
            this.f32076u = coloringActivity;
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationCancel(Animator animator) {
            Intrinsics.checkNotNullParameter(animator, olkfIKv.yXJ);
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationEnd(Animator animator) {
            String str;
            Intrinsics.checkNotNullParameter(animator, "animation");
            LottieAnimationView lottieAnimationView = this.f32075n.lottieLike;
            if (!this.f32076u.isLike()) {
                str = "result_like.json";
            } else {
                str = "result_like_2.json";
            }
            lottieAnimationView.setAnimation(str);
            this.f32075n.lottieLike.setProgress(0.0f);
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationRepeat(Animator animator) {
            Intrinsics.checkNotNullParameter(animator, "animation");
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationStart(Animator animator) {
            Intrinsics.checkNotNullParameter(animator, "animation");
        }
    }

    /* loaded from: classes2.dex */
    public static final class n extends ViewOutlineProvider {
        @Override // android.view.ViewOutlineProvider
        public void getOutline(View view, Outline outline) {
            int i10;
            if (outline != null) {
                int i11 = 0;
                if (view != null) {
                    i10 = view.getWidth();
                } else {
                    i10 = 0;
                }
                if (view != null) {
                    i11 = view.getHeight();
                }
                outline.setRoundRect(0, 0, i10, i11, com.base.module.tools.model_base.tools.m.a(12.0f));
            }
        }
    }

    /* loaded from: classes2.dex */
    public static final class o implements Transition.TransitionListener {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ ColoringActivity f32077a;

        public o(ColoringActivity coloringActivity) {
            this.f32077a = coloringActivity;
        }

        @Override // android.transition.Transition.TransitionListener
        public void onTransitionCancel(Transition transition) {
        }

        @Override // android.transition.Transition.TransitionListener
        public void onTransitionEnd(Transition transition) {
            if (this.f32077a.enterShow) {
                this.f32077a.getWindow().setBackgroundDrawable(new ColorDrawable(-1));
                if (Build.VERSION.SDK_INT >= 30) {
                    o.a(this.f32077a, false);
                }
                this.f32077a.enterShow = false;
                ColoringActivity.loadPreView$default(this.f32077a, false, 1, null);
                this.f32077a.loadColorData();
            }
        }

        @Override // android.transition.Transition.TransitionListener
        public void onTransitionPause(Transition transition) {
        }

        @Override // android.transition.Transition.TransitionListener
        public void onTransitionResume(Transition transition) {
        }

        @Override // android.transition.Transition.TransitionListener
        public void onTransitionStart(Transition transition) {
            if (!this.f32077a.enterShow) {
                this.f32077a.dispatchHidePaintView();
                Runnable mSendRefreshRunnable = this.f32077a.getMSendRefreshRunnable();
                if (mSendRefreshRunnable != null) {
                    mSendRefreshRunnable.run();
                }
                this.f32077a.setMSendRefreshRunnable(null);
                return;
            }
            this.f32077a.bgAlphaAnim();
            if (this.f32077a.getMShowAlpha()) {
                this.f32077a.showTransAlphaAnim();
            }
        }
    }

    /* loaded from: classes2.dex */
    public static final class p implements RequestListener<Bitmap> {

        /* renamed from: n  reason: collision with root package name */
        public final /* synthetic */ CustomPreviewImageView f32078n;

        public p(CustomPreviewImageView customPreviewImageView) {
            this.f32078n = customPreviewImageView;
        }

        /* renamed from: a */
        public boolean onResourceReady(Bitmap bitmap, Object obj, Target<Bitmap> target, DataSource dataSource, boolean z10) {
            Intrinsics.checkNotNullParameter(bitmap, Constants.VAST_RESOURCE);
            Intrinsics.checkNotNullParameter(obj, "model");
            Intrinsics.checkNotNullParameter(dataSource, "dataSource");
            this.f32078n.createBitmap(bitmap);
            return false;
        }

        @Override // com.bumptech.glide.request.RequestListener
        public boolean onLoadFailed(GlideException glideException, Object obj, Target<Bitmap> target, boolean z10) {
            Intrinsics.checkNotNullParameter(target, TypedValues.AttributesType.S_TARGET);
            return false;
        }
    }

    /* loaded from: classes2.dex */
    public static final class q extends gc.c<Integer> {

        /* renamed from: b  reason: collision with root package name */
        public final /* synthetic */ ColoringActivity f32079b;

        /* JADX INFO: 'super' call moved to the top of the method (can break code semantics) */
        public q(Object obj, ColoringActivity coloringActivity) {
            super(obj);
            this.f32079b = coloringActivity;
        }

        @Override // gc.c
        public void afterChange(KProperty<?> kProperty, Integer num, Integer num2) {
            TextView textView;
            TextView textView2;
            TextView textView3;
            AppCompatImageView appCompatImageView;
            TextView textView4;
            AppCompatImageView appCompatImageView2;
            TextView textView5;
            Intrinsics.checkNotNullParameter(kProperty, "property");
            int intValue = num2.intValue();
            int intValue2 = num.intValue();
            com.gpower.coloringbynumber.spf.c cVar = com.gpower.coloringbynumber.spf.c.f33083b;
            if (cVar.W0()) {
                ViewStubPaintToolsBinding mPaintToolsBinding = this.f32079b.getMPaintToolsBinding();
                if (!(mPaintToolsBinding == null || (textView5 = mPaintToolsBinding.tvAutoNum) == null)) {
                    q0.a(textView5, false);
                }
                ViewStubPaintToolsBinding mPaintToolsBinding2 = this.f32079b.getMPaintToolsBinding();
                if (mPaintToolsBinding2 != null && (appCompatImageView2 = mPaintToolsBinding2.ivAutoPaintsUnLimited) != null) {
                    q0.a(appCompatImageView2, true);
                    return;
                }
                return;
            }
            ViewStubPaintToolsBinding mPaintToolsBinding3 = this.f32079b.getMPaintToolsBinding();
            if (!(mPaintToolsBinding3 == null || (textView4 = mPaintToolsBinding3.tvAutoNum) == null)) {
                q0.a(textView4, true);
            }
            ViewStubPaintToolsBinding mPaintToolsBinding4 = this.f32079b.getMPaintToolsBinding();
            if (!(mPaintToolsBinding4 == null || (appCompatImageView = mPaintToolsBinding4.ivAutoPaintsUnLimited) == null)) {
                q0.a(appCompatImageView, false);
            }
            if (intValue > 0) {
                ViewStubPaintToolsBinding mPaintToolsBinding5 = this.f32079b.getMPaintToolsBinding();
                if (!(mPaintToolsBinding5 == null || (textView3 = mPaintToolsBinding5.tvAutoNum) == null)) {
                    textView3.setText(String.valueOf(intValue));
                }
                if (intValue2 > intValue && cVar.m0() > 0) {
                    cVar.V1(cVar.m0() - 1);
                    return;
                }
                return;
            }
            if (intValue2 > intValue && cVar.m0() > 0) {
                cVar.V1(cVar.m0() - 1);
            }
            if (AdsActivityManager.f33023a.r()) {
                TDEventUtils.l("hint_2");
                ViewStubPaintToolsBinding mPaintToolsBinding6 = this.f32079b.getMPaintToolsBinding();
                if (mPaintToolsBinding6 != null && (textView2 = mPaintToolsBinding6.tvAutoNum) != null) {
                    textView2.setText(this.f32079b.getString(R.string.color_ad_tag));
                    return;
                }
                return;
            }
            ViewStubPaintToolsBinding mPaintToolsBinding7 = this.f32079b.getMPaintToolsBinding();
            if (mPaintToolsBinding7 != null && (textView = mPaintToolsBinding7.tvAutoNum) != null) {
                textView.setText(this.f32079b.getString(R.string.zero));
            }
        }
    }

    /* loaded from: classes2.dex */
    public static final class r extends gc.c<Integer> {

        /* renamed from: b  reason: collision with root package name */
        public final /* synthetic */ ColoringActivity f32080b;

        /* JADX INFO: 'super' call moved to the top of the method (can break code semantics) */
        public r(Object obj, ColoringActivity coloringActivity) {
            super(obj);
            this.f32080b = coloringActivity;
        }

        @Override // gc.c
        public void afterChange(KProperty<?> kProperty, Integer num, Integer num2) {
            AppCompatImageView appCompatImageView;
            TextView textView;
            TextView textView2;
            TextView textView3;
            TextView textView4;
            AppCompatImageView appCompatImageView2;
            TextView textView5;
            Intrinsics.checkNotNullParameter(kProperty, "property");
            int intValue = num2.intValue();
            int intValue2 = num.intValue();
            com.gpower.coloringbynumber.spf.c cVar = com.gpower.coloringbynumber.spf.c.f33083b;
            if (cVar.W0() || this.f32080b.isBoughtCollectPackage()) {
                ViewStubPaintToolsBinding mPaintToolsBinding = this.f32080b.getMPaintToolsBinding();
                if (!(mPaintToolsBinding == null || (textView = mPaintToolsBinding.tvHintNum) == null)) {
                    q0.a(textView, false);
                }
                ViewStubPaintToolsBinding mPaintToolsBinding2 = this.f32080b.getMPaintToolsBinding();
                if (mPaintToolsBinding2 != null && (appCompatImageView = mPaintToolsBinding2.ivHintsUnLimited) != null) {
                    q0.a(appCompatImageView, true);
                    return;
                }
                return;
            }
            ViewStubPaintToolsBinding mPaintToolsBinding3 = this.f32080b.getMPaintToolsBinding();
            if (!(mPaintToolsBinding3 == null || (textView5 = mPaintToolsBinding3.tvHintNum) == null)) {
                q0.a(textView5, true);
            }
            ViewStubPaintToolsBinding mPaintToolsBinding4 = this.f32080b.getMPaintToolsBinding();
            if (!(mPaintToolsBinding4 == null || (appCompatImageView2 = mPaintToolsBinding4.ivHintsUnLimited) == null)) {
                q0.a(appCompatImageView2, false);
            }
            if (intValue > 0) {
                ViewStubPaintToolsBinding mPaintToolsBinding5 = this.f32080b.getMPaintToolsBinding();
                if (!(mPaintToolsBinding5 == null || (textView4 = mPaintToolsBinding5.tvHintNum) == null)) {
                    textView4.setText(String.valueOf(intValue));
                }
                if (intValue2 <= intValue) {
                    return;
                }
                if (cVar.n0() > 0) {
                    cVar.W1(cVar.n0() - 1);
                } else if (cVar.F0() > 0) {
                    cVar.o2(cVar.F0() - 1);
                } else {
                    this.f32080b.getGoodsBoughtViewModel().updateReduceHintGoodsBought();
                }
            } else {
                if (intValue2 > intValue) {
                    if (cVar.n0() > 0) {
                        cVar.W1(cVar.n0() - 1);
                    } else if (cVar.F0() > 0) {
                        cVar.o2(cVar.F0() - 1);
                    } else {
                        this.f32080b.getGoodsBoughtViewModel().updateReduceHintGoodsBought();
                    }
                }
                if (AdsActivityManager.f33023a.r()) {
                    TDEventUtils.l(k2.g.f96925y);
                    ViewStubPaintToolsBinding mPaintToolsBinding6 = this.f32080b.getMPaintToolsBinding();
                    if (mPaintToolsBinding6 != null && (textView3 = mPaintToolsBinding6.tvHintNum) != null) {
                        textView3.setText(this.f32080b.getString(R.string.color_ad_tag));
                        return;
                    }
                    return;
                }
                ViewStubPaintToolsBinding mPaintToolsBinding7 = this.f32080b.getMPaintToolsBinding();
                if (mPaintToolsBinding7 != null && (textView2 = mPaintToolsBinding7.tvHintNum) != null) {
                    textView2.setText(this.f32080b.getString(R.string.zero));
                }
            }
        }
    }

    /* loaded from: classes2.dex */
    public static final class s implements f.b {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ ColoringActivity f32081a;

        public s(ColoringActivity coloringActivity) {
            this.f32081a = coloringActivity;
        }

        @Override // com.gpower.coloringbynumber.tools.f.b
        public void a(f.a aVar) {
        }

        @Override // com.gpower.coloringbynumber.tools.f.b
        public void b(f.a aVar) {
        }

        @Override // com.gpower.coloringbynumber.tools.f.b
        public void c(f.a aVar) {
            this.f32081a.mLoadingAnimResult = true;
            Runnable runnable = this.f32081a.mStartRunnable;
            if (runnable != null) {
                runnable.run();
            }
        }
    }

    /* loaded from: classes2.dex */
    public static final class t implements Animator.AnimatorListener {

        /* renamed from: n  reason: collision with root package name */
        public final /* synthetic */ ColorPaintedView f32087n;

        public t(ColorPaintedView colorPaintedView) {
            this.f32087n = colorPaintedView;
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationCancel(Animator animator) {
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationEnd(Animator animator) {
            this.f32087n.setVisibility(4);
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationRepeat(Animator animator) {
        }

        @Override // android.animation.Animator.AnimatorListener
        public void onAnimationStart(Animator animator) {
        }
    }

    public ColoringActivity() {
        o0.a aVar = o0.a.f99347a;
        this.abTestTipProp = aVar.h();
        this.abTestUsAdVert = aVar.h();
        this.abTestSurpriseColor = aVar.h();
        this.abTestSubscriptionPage = aVar.h();
        this.abTestResultCollection = aVar.h();
        this.mPlayerStatus = 1;
        this.curPaintStage = 1;
        this.mLongColorAnim = new int[]{R.drawable.loading9_00, R.drawable.loading9_01, R.drawable.loading9_02, R.drawable.loading9_03, R.drawable.loading9_04, R.drawable.loading9_05, R.drawable.loading9_06, R.drawable.loading9_07, R.drawable.loading9_08, R.drawable.loading9_09, R.drawable.loading9_10, R.drawable.loading9_11, R.drawable.loading9_12, R.drawable.loading9_13, R.drawable.loading9_14, R.drawable.loading9_15, R.drawable.loading9_16, R.drawable.loading9_17, R.drawable.loading9_18, R.drawable.loading9_19, R.drawable.loading9_20, R.drawable.loading9_21, R.drawable.loading9_22, R.drawable.loading9_23, R.drawable.loading9_24, R.drawable.loading9_25, R.drawable.loading9_26, R.drawable.loading9_27, R.drawable.loading9_28, R.drawable.loading9_29, R.drawable.loading9_30, R.drawable.loading9_31, R.drawable.loading9_32, R.drawable.loading9_33, R.drawable.loading9_34, R.drawable.loading9_35, R.drawable.loading9_36, R.drawable.loading9_37, R.drawable.loading9_38, R.drawable.loading9_39, R.drawable.loading9_40, R.drawable.loading9_41, R.drawable.loading9_42, R.drawable.loading9_43, R.drawable.loading9_44, R.drawable.loading9_45, R.drawable.loading9_46, R.drawable.loading9_47, R.drawable.loading9_48, R.drawable.loading9_49, R.drawable.loading9_50, R.drawable.loading9_51, R.drawable.loading9_52, R.drawable.loading9_53, R.drawable.loading9_54, R.drawable.loading9_55, R.drawable.loading9_56, R.drawable.loading9_57, R.drawable.loading9_58, R.drawable.loading9_59, R.drawable.loading9_60, R.drawable.loading9_61, R.drawable.loading9_62, R.drawable.loading9_63, R.drawable.loading9_64, R.drawable.loading9_65, R.drawable.loading9_66, R.drawable.loading9_67, R.drawable.loading9_68, R.drawable.loading9_69};
        this.mColorAnim = new int[]{R.drawable.loading00, R.drawable.loading01, R.drawable.loading02, R.drawable.loading03, R.drawable.loading04, R.drawable.loading05, R.drawable.loading06, R.drawable.loading07, R.drawable.loading08, R.drawable.loading09, R.drawable.loading10, R.drawable.loading11, R.drawable.loading12, R.drawable.loading13, R.drawable.loading14, R.drawable.loading15, R.drawable.loading16, R.drawable.loading17, R.drawable.loading18, R.drawable.loading19, R.drawable.loading20, R.drawable.loading21, R.drawable.loading22, R.drawable.loading23, R.drawable.loading24, R.drawable.loading25, R.drawable.loading26, R.drawable.loading27, R.drawable.loading28, R.drawable.loading29, R.drawable.loading30, R.drawable.loading31, R.drawable.loading32, R.drawable.loading33, R.drawable.loading34, R.drawable.loading35, R.drawable.loading36, R.drawable.loading37, R.drawable.loading38, R.drawable.loading39, R.drawable.loading40, R.drawable.loading41, R.drawable.loading42, R.drawable.loading43, R.drawable.loading44, R.drawable.loading45, R.drawable.loading46, R.drawable.loading47, R.drawable.loading48, R.drawable.loading49, R.drawable.loading50, R.drawable.loading51, R.drawable.loading52, R.drawable.loading53, R.drawable.loading54, R.drawable.loading55, R.drawable.loading56, R.drawable.loading57, R.drawable.loading58, R.drawable.loading59, R.drawable.loading60, R.drawable.loading61, R.drawable.loading62, R.drawable.loading63, R.drawable.loading64, R.drawable.loading65, R.drawable.loading66, R.drawable.loading67, R.drawable.loading68, R.drawable.loading69};
        gc.a aVar2 = gc.a.f88141a;
        this.mAutoPaintFindNum$delegate = new q(-1, this);
        this.mColoringPropsFindNum$delegate = new r(-1, this);
        this.showPropGitDuration = 20000;
        this.mPopupPropsGift$delegate = a0.c(new Function0() { // from class: com.gpower.coloringbynumber.activity.e0
            @Override // kotlin.jvm.functions.Function0
            public final Object invoke() {
                return ColoringActivity.mPopupPropsGift_delegate$lambda$135(ColoringActivity.this);
            }
        });
        this.mPopupClaimProp$delegate = a0.c(new Function0() { // from class: com.gpower.coloringbynumber.activity.f0
            @Override // kotlin.jvm.functions.Function0
            public final Object invoke() {
                return ColoringActivity.mPopupClaimProp_delegate$lambda$136(ColoringActivity.this);
            }
        });
        this.exitDelay = 800;
        this.mColorListener = new e(this);
    }

    /* access modifiers changed from: private */
    /* JADX WARNING: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARNING: Removed duplicated region for block: B:12:0x0030  */
    /* JADX WARNING: Removed duplicated region for block: B:13:0x0035  */
    /* JADX WARNING: Removed duplicated region for block: B:14:0x003a  */
    /* JADX WARNING: Removed duplicated region for block: B:15:0x003f  */
    /* JADX WARNING: Removed duplicated region for block: B:16:0x0044  */
    /* JADX WARNING: Removed duplicated region for block: B:17:0x004d  */
    /* JADX WARNING: Removed duplicated region for block: B:18:0x0052  */
    /* JADX WARNING: Removed duplicated region for block: B:19:0x0057  */
    /* JADX WARNING: Removed duplicated region for block: B:20:0x005c  */
    /* JADX WARNING: Removed duplicated region for block: B:21:0x0065  */
    /* JADX WARNING: Removed duplicated region for block: B:22:0x006e  */
    /* JADX WARNING: Removed duplicated region for block: B:23:0x0077  */
    /* JADX WARNING: Removed duplicated region for block: B:24:0x0080  */
    /* JADX WARNING: Removed duplicated region for block: B:25:0x0089  */
    /* JADX WARNING: Removed duplicated region for block: B:45:0x0117 A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:46:0x0118  */
    /* JADX WARNING: Removed duplicated region for block: B:49:0x0134 A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:50:0x0135  */
    /* JADX WARNING: Removed duplicated region for block: B:53:0x0151 A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:54:0x0152  */
    /* JADX WARNING: Removed duplicated region for block: B:57:0x016e A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:58:0x016f  */
    /* JADX WARNING: Removed duplicated region for block: B:61:0x0191 A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:64:0x01a5 A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:67:0x01ae  */
    /* JADX WARNING: Removed duplicated region for block: B:72:0x01da A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:73:0x01db  */
    /* JADX WARNING: Removed duplicated region for block: B:76:0x01f3  */
    /* JADX WARNING: Removed duplicated region for block: B:83:0x0231 A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:93:0x0271 A[RETURN] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object abTestInfo(kotlin.coroutines.Continuation<? super kotlin.Unit> r11) {
        /*
        // Method dump skipped, instructions count: 664
        */
        throw new UnsupportedOperationException("Method not decompiled: com.gpower.coloringbynumber.activity.ColoringActivity.abTestInfo(kotlin.coroutines.Continuation):java.lang.Object");
    }

    /* access modifiers changed from: private */
    /* JADX WARNING: Removed duplicated region for block: B:10:0x0025  */
    /* JADX WARNING: Removed duplicated region for block: B:16:0x003f  */
    /* JADX WARNING: Removed duplicated region for block: B:21:0x0074 A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:22:0x0075  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object adVertAbTestB(boolean r10, kotlin.coroutines.Continuation<? super java.lang.Integer> r11) {
        /*
            r9 = this;
            boolean r0 = r11 instanceof com.gpower.coloringbynumber.activity.ColoringActivity$adVertAbTestB$1
            if (r0 == 0) goto L_0x0013
            r0 = r11
            com.gpower.coloringbynumber.activity.ColoringActivity$adVertAbTestB$1 r0 = (com.gpower.coloringbynumber.activity.ColoringActivity$adVertAbTestB$1) r0
            int r1 = r0.label
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.label = r1
            goto L_0x0018
        L_0x0013:
            com.gpower.coloringbynumber.activity.ColoringActivity$adVertAbTestB$1 r0 = new com.gpower.coloringbynumber.activity.ColoringActivity$adVertAbTestB$1
            r0.<init>(r9, r11)
        L_0x0018:
            java.lang.Object r11 = r0.result
            java.lang.Object r1 = kotlin.coroutines.intrinsics.b.l()
            int r2 = r0.label
            r3 = -1
            r4 = 2
            r5 = 1
            if (r2 == 0) goto L_0x003f
            if (r2 == r5) goto L_0x0039
            if (r2 != r4) goto L_0x0031
            int r10 = r0.I$0
            boolean r0 = r0.Z$0
            kotlin.s0.n(r11)
            goto L_0x0079
        L_0x0031:
            java.lang.IllegalStateException r10 = new java.lang.IllegalStateException
            java.lang.String r11 = "call to 'resume' before 'invoke' with coroutine"
            r10.<init>(r11)
            throw r10
        L_0x0039:
            boolean r10 = r0.Z$0
            kotlin.s0.n(r11)
            goto L_0x0057
        L_0x003f:
            kotlin.s0.n(r11)
            com.gpower.coloringbynumber.spf.DataStoreManager r11 = com.gpower.coloringbynumber.spf.DataStoreManager.f33038a
            androidx.datastore.preferences.core.Preferences$Key r2 = com.gpower.coloringbynumber.spf.b.s()
            java.lang.Integer r6 = kotlin.coroutines.jvm.internal.a.f(r3)
            r0.Z$0 = r10
            r0.label = r5
            java.lang.Object r11 = r11.b(r2, r6, r0)
            if (r11 != r1) goto L_0x0057
            return r1
        L_0x0057:
            java.lang.Number r11 = (java.lang.Number) r11
            int r11 = r11.intValue()
            com.gpower.coloringbynumber.spf.DataStoreManager r2 = com.gpower.coloringbynumber.spf.DataStoreManager.f33038a
            androidx.datastore.preferences.core.Preferences$Key r6 = com.gpower.coloringbynumber.spf.b.q()
            r7 = 0
            java.lang.Integer r7 = kotlin.coroutines.jvm.internal.a.f(r7)
            r0.Z$0 = r10
            r0.I$0 = r11
            r0.label = r4
            java.lang.Object r0 = r2.b(r6, r7, r0)
            if (r0 != r1) goto L_0x0075
            return r1
        L_0x0075:
            r8 = r0
            r0 = r10
            r10 = r11
            r11 = r8
        L_0x0079:
            java.lang.Number r11 = (java.lang.Number) r11
            int r11 = r11.intValue()
            r1 = 0
            if (r10 == r3) goto L_0x00c4
            if (r11 != 0) goto L_0x0085
            goto L_0x00c4
        L_0x0085:
            r2 = 3
            r3 = 506(0x1fa, float:7.09E-43)
            if (r10 != 0) goto L_0x0094
            if (r11 < r2) goto L_0x008f
            if (r0 != 0) goto L_0x008f
            goto L_0x0093
        L_0x008f:
            java.lang.Integer r1 = kotlin.coroutines.jvm.internal.a.f(r3)
        L_0x0093:
            return r1
        L_0x0094:
            if (r10 == r5) goto L_0x00b9
            r6 = 4
            if (r10 != r6) goto L_0x009a
            goto L_0x00b9
        L_0x009a:
            if (r10 != r2) goto L_0x00a9
            if (r11 < r2) goto L_0x00a4
            int r11 = r11 % r4
            if (r11 != r5) goto L_0x00a4
            if (r0 != 0) goto L_0x00a4
            goto L_0x00a8
        L_0x00a4:
            java.lang.Integer r1 = kotlin.coroutines.jvm.internal.a.f(r3)
        L_0x00a8:
            return r1
        L_0x00a9:
            if (r10 == r4) goto L_0x00b0
            r2 = 5
            if (r10 < r2) goto L_0x00af
            goto L_0x00b0
        L_0x00af:
            return r1
        L_0x00b0:
            if (r11 != r5) goto L_0x00b8
            if (r0 == 0) goto L_0x00b8
            java.lang.Integer r1 = kotlin.coroutines.jvm.internal.a.f(r3)
        L_0x00b8:
            return r1
        L_0x00b9:
            if (r11 == r5) goto L_0x00c4
            if (r11 < r4) goto L_0x00c0
            if (r0 != 0) goto L_0x00c0
            goto L_0x00c4
        L_0x00c0:
            java.lang.Integer r1 = kotlin.coroutines.jvm.internal.a.f(r3)
        L_0x00c4:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: com.gpower.coloringbynumber.activity.ColoringActivity.adVertAbTestB(boolean, kotlin.coroutines.Continuation):java.lang.Object");
    }

    /* access modifiers changed from: private */
    /* JADX WARNING: Removed duplicated region for block: B:10:0x0025  */
    /* JADX WARNING: Removed duplicated region for block: B:16:0x003f  */
    /* JADX WARNING: Removed duplicated region for block: B:21:0x0074 A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:22:0x0075  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object adVertAbTestC(boolean r10, kotlin.coroutines.Continuation<? super java.lang.Integer> r11) {
        /*
            r9 = this;
            boolean r0 = r11 instanceof com.gpower.coloringbynumber.activity.ColoringActivity$adVertAbTestC$1
            if (r0 == 0) goto L_0x0013
            r0 = r11
            com.gpower.coloringbynumber.activity.ColoringActivity$adVertAbTestC$1 r0 = (com.gpower.coloringbynumber.activity.ColoringActivity$adVertAbTestC$1) r0
            int r1 = r0.label
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.label = r1
            goto L_0x0018
        L_0x0013:
            com.gpower.coloringbynumber.activity.ColoringActivity$adVertAbTestC$1 r0 = new com.gpower.coloringbynumber.activity.ColoringActivity$adVertAbTestC$1
            r0.<init>(r9, r11)
        L_0x0018:
            java.lang.Object r11 = r0.result
            java.lang.Object r1 = kotlin.coroutines.intrinsics.b.l()
            int r2 = r0.label
            r3 = -1
            r4 = 2
            r5 = 1
            if (r2 == 0) goto L_0x003f
            if (r2 == r5) goto L_0x0039
            if (r2 != r4) goto L_0x0031
            int r10 = r0.I$0
            boolean r0 = r0.Z$0
            kotlin.s0.n(r11)
            goto L_0x0079
        L_0x0031:
            java.lang.IllegalStateException r10 = new java.lang.IllegalStateException
            java.lang.String r11 = "call to 'resume' before 'invoke' with coroutine"
            r10.<init>(r11)
            throw r10
        L_0x0039:
            boolean r10 = r0.Z$0
            kotlin.s0.n(r11)
            goto L_0x0057
        L_0x003f:
            kotlin.s0.n(r11)
            com.gpower.coloringbynumber.spf.DataStoreManager r11 = com.gpower.coloringbynumber.spf.DataStoreManager.f33038a
            androidx.datastore.preferences.core.Preferences$Key r2 = com.gpower.coloringbynumber.spf.b.s()
            java.lang.Integer r6 = kotlin.coroutines.jvm.internal.a.f(r3)
            r0.Z$0 = r10
            r0.label = r5
            java.lang.Object r11 = r11.b(r2, r6, r0)
            if (r11 != r1) goto L_0x0057
            return r1
        L_0x0057:
            java.lang.Number r11 = (java.lang.Number) r11
            int r11 = r11.intValue()
            com.gpower.coloringbynumber.spf.DataStoreManager r2 = com.gpower.coloringbynumber.spf.DataStoreManager.f33038a
            androidx.datastore.preferences.core.Preferences$Key r6 = com.gpower.coloringbynumber.spf.b.q()
            r7 = 0
            java.lang.Integer r7 = kotlin.coroutines.jvm.internal.a.f(r7)
            r0.Z$0 = r10
            r0.I$0 = r11
            r0.label = r4
            java.lang.Object r0 = r2.b(r6, r7, r0)
            if (r0 != r1) goto L_0x0075
            return r1
        L_0x0075:
            r8 = r0
            r0 = r10
            r10 = r11
            r11 = r8
        L_0x0079:
            java.lang.Number r11 = (java.lang.Number) r11
            int r11 = r11.intValue()
            r1 = 0
            if (r10 == r3) goto L_0x00bf
            if (r11 != 0) goto L_0x0085
            goto L_0x00bf
        L_0x0085:
            r2 = 3
            r3 = 506(0x1fa, float:7.09E-43)
            if (r10 != 0) goto L_0x0094
            if (r11 < r2) goto L_0x008f
            if (r0 != 0) goto L_0x008f
            goto L_0x0093
        L_0x008f:
            java.lang.Integer r1 = kotlin.coroutines.jvm.internal.a.f(r3)
        L_0x0093:
            return r1
        L_0x0094:
            if (r10 != r5) goto L_0x009f
            if (r11 != r5) goto L_0x009e
            if (r0 == 0) goto L_0x009e
            java.lang.Integer r1 = kotlin.coroutines.jvm.internal.a.f(r3)
        L_0x009e:
            return r1
        L_0x009f:
            if (r10 == r4) goto L_0x00b4
            r6 = 4
            if (r10 != r6) goto L_0x00a5
            goto L_0x00b4
        L_0x00a5:
            if (r10 != r2) goto L_0x00b3
            if (r11 < r2) goto L_0x00af
            int r11 = r11 % r4
            if (r11 != r5) goto L_0x00af
            if (r0 != 0) goto L_0x00af
            goto L_0x00b3
        L_0x00af:
            java.lang.Integer r1 = kotlin.coroutines.jvm.internal.a.f(r3)
        L_0x00b3:
            return r1
        L_0x00b4:
            if (r11 == r5) goto L_0x00bf
            if (r11 < r4) goto L_0x00bb
            if (r0 != 0) goto L_0x00bb
            goto L_0x00bf
        L_0x00bb:
            java.lang.Integer r1 = kotlin.coroutines.jvm.internal.a.f(r3)
        L_0x00bf:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: com.gpower.coloringbynumber.activity.ColoringActivity.adVertAbTestC(boolean, kotlin.coroutines.Continuation):java.lang.Object");
    }

    /* access modifiers changed from: private */
    public final void bgAlphaAnim() {
        ObjectAnimator ofObject = ObjectAnimator.ofObject(getWindow().getDecorView(), "backgroundColor", new ArgbEvaluator(), 0, -1);
        ofObject.setDuration(1000L);
        ofObject.start();
    }

    private final void bindExoPlayer() {
        y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(this), z0.c(), null, new ColoringActivity$bindExoPlayer$1(this, null), 2, null);
    }

    /* JADX INFO: Can't fix incorrect switch cases order, some code will duplicate */
    private final void categoryAchievement() {
        List<String> categoryNameInfo;
        Integer num;
        BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo = this.mRelationBean;
        if (!(beanResourceRelationTemplateInfo == null || (categoryNameInfo = beanResourceRelationTemplateInfo.categoryNameInfo(this.mCalendarIdList, this.mCollectIdList)) == null)) {
            for (String str : categoryNameInfo) {
                switch (str.hashCode()) {
                    case -1968740153:
                        if (str.equals(k2.j.f96989y)) {
                            num = 19;
                            break;
                        }
                        num = null;
                        break;
                    case -1901906836:
                        if (str.equals(k2.j.F)) {
                            num = 12;
                            break;
                        }
                        num = null;
                        break;
                    case -726803703:
                        if (str.equals(k2.j.G)) {
                            num = 11;
                            break;
                        }
                        num = null;
                        break;
                    case -343811943:
                        if (str.equals(k2.j.A)) {
                            num = 17;
                            break;
                        }
                        num = null;
                        break;
                    case -113680546:
                        if (str.equals(k2.j.H)) {
                            num = 3;
                            break;
                        }
                        num = null;
                        break;
                    case 2195582:
                        if (str.equals(k2.j.C)) {
                            num = 15;
                            break;
                        }
                        num = null;
                        break;
                    case 68052152:
                        if (str.equals(k2.j.f96990z)) {
                            num = 18;
                            break;
                        }
                        num = null;
                        break;
                    case 190508807:
                        if (str.equals(k2.j.B)) {
                            num = 16;
                            break;
                        }
                        num = null;
                        break;
                    case 807717335:
                        if (str.equals(k2.j.f96988x)) {
                            num = 10;
                            break;
                        }
                        num = null;
                        break;
                    case 873562992:
                        if (str.equals(k2.j.D)) {
                            num = 14;
                            break;
                        }
                        num = null;
                        break;
                    case 898853208:
                        if (str.equals(k2.j.E)) {
                            num = 13;
                            break;
                        }
                        num = null;
                        break;
                    default:
                        num = null;
                        break;
                }
                if (num != null) {
                    int intValue = num.intValue();
                    ViewModelAchievement i10 = App.I.b().i();
                    if (i10 != null) {
                        i10.achievementTypeAddProgress(intValue);
                    }
                }
            }
        }
    }

    private final void changeAnimStatus() {
        String str = this.clickLocation;
        if (str != null) {
            TDEventUtils.f33166a.c(k2.d.f96795l0, "location", str, "state", Integer.valueOf(this.showAnimLoading ? 1 : 0));
            if (this.showAnimLoading) {
                skipAnim();
            } else {
                refreshAnim();
            }
        }
    }

    public static /* synthetic */ void checkPathCount$default(ColoringActivity coloringActivity, Float f10, int i10, Object obj) {
        if (obj == null) {
            if ((i10 & 1) != 0) {
                f10 = null;
            }
            coloringActivity.checkPathCount(f10);
            return;
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: checkPathCount");
    }

    private final void checkUserPermission(Function0<Unit> function0) {
        if (checkWritePermission()) {
            function0.invoke();
        } else {
            this.mPermissionLauncher.launch(Build.VERSION.SDK_INT >= 33 ? new String[]{"android.permission.READ_MEDIA_IMAGES"} : new String[]{"android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.READ_EXTERNAL_STORAGE"});
        }
    }

    private final boolean checkWritePermission() {
        if (Build.VERSION.SDK_INT >= 33) {
            if (ContextCompat.checkSelfPermission(this, "android.permission.READ_MEDIA_IMAGES") != 0) {
                return false;
            }
        } else if (!(ContextCompat.checkSelfPermission(this, "android.permission.WRITE_EXTERNAL_STORAGE") == 0 && ContextCompat.checkSelfPermission(this, "android.permission.READ_EXTERNAL_STORAGE") == 0)) {
            return false;
        }
        return true;
    }

    /* access modifiers changed from: private */
    public final ExoPlayer createOptInExoPlayer() {
        ExoPlayer build = new ExoPlayer.Builder(this).setRenderersFactory(new DefaultRenderersFactory(this).setEnableDecoderFallback(true)).build();
        Intrinsics.checkNotNullExpressionValue(build, "build(...)");
        return build;
    }

    /* access modifiers changed from: private */
    public final void createSurpriseColorBinding() {
        ViewStubSurpriseColorBinding viewStubSurpriseColorBinding;
        ViewStub viewStub;
        View inflate;
        if (Intrinsics.g(this.abTestSurpriseColor, o0.a.f99347a.b()) && this.mSurpriseColoringBinding == null) {
            ViewStubPaintToolsBinding viewStubPaintToolsBinding = this.mPaintToolsBinding;
            if (viewStubPaintToolsBinding == null || (viewStub = viewStubPaintToolsBinding.surpriseColorViewStub) == null || (inflate = viewStub.inflate()) == null) {
                viewStubSurpriseColorBinding = null;
            } else {
                viewStubSurpriseColorBinding = ViewStubSurpriseColorBinding.bind(inflate);
            }
            this.mSurpriseColoringBinding = viewStubSurpriseColorBinding;
        }
    }

    /* access modifiers changed from: private */
    public static final Unit dispatchAutoPaint$lambda$150(ColoringActivity coloringActivity) {
        coloringActivity.propGiftRestartCalculationTime(5);
        return Unit.f97091a;
    }

    public static /* synthetic */ void dispatchZoomCurSelectArea$default(ColoringActivity coloringActivity, long j10, Float f10, int i10, Object obj) {
        if (obj == null) {
            if ((i10 & 1) != 0) {
                j10 = 200;
            }
            if ((i10 & 2) != 0) {
                f10 = null;
            }
            coloringActivity.dispatchZoomCurSelectArea(j10, f10);
            return;
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: dispatchZoomCurSelectArea");
    }

    /* access modifiers changed from: private */
    public final void enterResultView(boolean z10) {
        runOnUiThread(new Runnable(z10) { // from class: com.gpower.coloringbynumber.activity.p0

            /* renamed from: u  reason: collision with root package name */
            public final /* synthetic */ boolean f32205u;

            {
                this.f32205u = r2;
            }

            @Override // java.lang.Runnable
            public final void run() {
                ColoringActivity.enterResultView$lambda$119(ColoringActivity.this, this.f32205u);
            }
        });
    }

    /* access modifiers changed from: private */
    public static final void enterResultView$lambda$119(ColoringActivity coloringActivity, boolean z10) {
        coloringActivity.dispatchHideLineAnim(z10, new Function0() { // from class: com.gpower.coloringbynumber.activity.e1
            @Override // kotlin.jvm.functions.Function0
            public final Object invoke() {
                return ColoringActivity.enterResultView$lambda$119$lambda$118(ColoringActivity.this);
            }
        });
    }

    /* access modifiers changed from: private */
    public static final Unit enterResultView$lambda$119$lambda$118(ColoringActivity coloringActivity) {
        BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo;
        ImageView imageView;
        ImageView imageView2;
        ImageView imageView3;
        ViewStubFinishViewBinding viewStubFinishViewBinding;
        ImageView imageView4;
        ImageView imageView5;
        BeanTemplateInfoDBM beanTemplateInfo;
        PlayerView playerView;
        PlayerView playerView2;
        ConstraintLayout constraintLayout;
        TextView textView;
        ImageView imageView6;
        boolean g10 = Intrinsics.g(coloringActivity.clickLocation, k2.g.f96927y1);
        coloringActivity.clickLocation = k2.g.f96930z1;
        ViewStubFinishViewBinding viewStubFinishViewBinding2 = coloringActivity.mFinishResultBinding;
        if (!(viewStubFinishViewBinding2 == null || (imageView6 = viewStubFinishViewBinding2.editPathFinishSkipIcon) == null)) {
            imageView6.setVisibility(8);
        }
        ViewStubFinishViewBinding viewStubFinishViewBinding3 = coloringActivity.mFinishResultBinding;
        if (!(viewStubFinishViewBinding3 == null || (textView = viewStubFinishViewBinding3.editPathFinishSkipTv) == null)) {
            textView.setVisibility(8);
        }
        ViewStubFinishViewBinding viewStubFinishViewBinding4 = coloringActivity.mFinishResultBinding;
        if (!(viewStubFinishViewBinding4 == null || (constraintLayout = viewStubFinishViewBinding4.editPathFinishFunLayout) == null)) {
            constraintLayout.setVisibility(0);
        }
        ExoPlayer exoPlayer = coloringActivity.mPlayer;
        ImageView imageView7 = null;
        if (exoPlayer == null) {
            coloringActivity.showAnimLoading = false;
        } else if (coloringActivity.mPlayerStatus == 3) {
            if (exoPlayer != null) {
                exoPlayer.play();
            }
            ViewStubFinishViewBinding viewStubFinishViewBinding5 = coloringActivity.mFinishResultBinding;
            if (!(viewStubFinishViewBinding5 == null || (playerView2 = viewStubFinishViewBinding5.editPathPlayerView) == null)) {
                playerView2.setVisibility(0);
            }
            ViewStubFinishViewBinding viewStubFinishViewBinding6 = coloringActivity.mFinishResultBinding;
            if (viewStubFinishViewBinding6 != null) {
                playerView = viewStubFinishViewBinding6.editPathPlayerView;
            } else {
                playerView = null;
            }
            ObjectAnimator ofFloat = ObjectAnimator.ofFloat(playerView, "alpha", 0.0f, 1.0f);
            AnimatorSet animatorSet = new AnimatorSet();
            animatorSet.play(ofFloat);
            animatorSet.setDuration(500L);
            animatorSet.addListener(new b(coloringActivity));
            animatorSet.start();
        } else {
            coloringActivity.showAnimLoading = false;
        }
        com.gpower.coloringbynumber.spf.c cVar = com.gpower.coloringbynumber.spf.c.f33083b;
        if (cVar.W0() || ((beanResourceRelationTemplateInfo = coloringActivity.mRelationBean) != null && (beanTemplateInfo = beanResourceRelationTemplateInfo.getBeanTemplateInfo()) != null && beanTemplateInfo.getRemoveWaterMark())) {
            return Unit.f97091a;
        }
        if (g10 && !Intrinsics.g(cVar.r(), o0.a.f99347a.b())) {
            TDEventUtils.l("watermark");
        }
        ViewStubFinishViewBinding viewStubFinishViewBinding7 = coloringActivity.mFinishResultBinding;
        if (!((viewStubFinishViewBinding7 == null || (imageView5 = viewStubFinishViewBinding7.editPathWaterMaskIcon) == null || imageView5.getVisibility() != 8) && ((viewStubFinishViewBinding = coloringActivity.mFinishResultBinding) == null || (imageView4 = viewStubFinishViewBinding.editPathWaterMaskCloseIcon) == null || imageView4.getVisibility() != 8))) {
            ViewStubFinishViewBinding viewStubFinishViewBinding8 = coloringActivity.mFinishResultBinding;
            if (!(viewStubFinishViewBinding8 == null || (imageView3 = viewStubFinishViewBinding8.editPathWaterMaskIcon) == null)) {
                imageView3.setVisibility(0);
            }
            ViewStubFinishViewBinding viewStubFinishViewBinding9 = coloringActivity.mFinishResultBinding;
            if (!(viewStubFinishViewBinding9 == null || (imageView2 = viewStubFinishViewBinding9.editPathWaterMaskCloseIcon) == null)) {
                imageView2.setVisibility(0);
            }
            ViewStubFinishViewBinding viewStubFinishViewBinding10 = coloringActivity.mFinishResultBinding;
            if (viewStubFinishViewBinding10 != null) {
                imageView = viewStubFinishViewBinding10.editPathWaterMaskIcon;
            } else {
                imageView = null;
            }
            ObjectAnimator ofFloat2 = ObjectAnimator.ofFloat(imageView, "alpha", 0.0f, 1.0f);
            ViewStubFinishViewBinding viewStubFinishViewBinding11 = coloringActivity.mFinishResultBinding;
            if (viewStubFinishViewBinding11 != null) {
                imageView7 = viewStubFinishViewBinding11.editPathWaterMaskCloseIcon;
            }
            ObjectAnimator ofFloat3 = ObjectAnimator.ofFloat(imageView7, "alpha", 0.0f, 1.0f);
            AnimatorSet animatorSet2 = new AnimatorSet();
            animatorSet2.playTogether(ofFloat2, ofFloat3);
            animatorSet2.setDuration(500L);
            animatorSet2.addListener(new c());
            animatorSet2.start();
        }
        return Unit.f97091a;
    }

    private final void eventReportClickSaveOrShare(String str) {
        String str2;
        TDEventUtils tDEventUtils = TDEventUtils.f33166a;
        BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo = this.mRelationBean;
        if (beanResourceRelationTemplateInfo == null || (str2 = beanResourceRelationTemplateInfo.itemCode()) == null) {
            str2 = "";
        }
        tDEventUtils.c(k2.d.f96783f0, "location", k2.g.f96897o1, "name", str, k2.e.f96825j, str2);
    }

    private final void eventReportFinishPic() {
        String str;
        String str2;
        String str3;
        String str4;
        String templateType;
        String categoryNames;
        String itemCode;
        TDEventUtils tDEventUtils = TDEventUtils.f33166a;
        BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo = this.mRelationBean;
        if (beanResourceRelationTemplateInfo == null || (itemCode = beanResourceRelationTemplateInfo.itemCode()) == null) {
            str = "";
        } else {
            str = itemCode;
        }
        V25ServerManager v25ServerManager = V25ServerManager.f32776a;
        BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo2 = this.mRelationBean;
        if (beanResourceRelationTemplateInfo2 == null || (str2 = beanResourceRelationTemplateInfo2.categoryIdList()) == null) {
            str2 = "";
        }
        String B = v25ServerManager.B(str2);
        BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo3 = this.mRelationBean;
        if (beanResourceRelationTemplateInfo3 == null || (categoryNames = beanResourceRelationTemplateInfo3.categoryNames()) == null) {
            str3 = "";
        } else {
            str3 = categoryNames;
        }
        BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo4 = this.mRelationBean;
        if (beanResourceRelationTemplateInfo4 == null || (templateType = beanResourceRelationTemplateInfo4.templateType()) == null) {
            str4 = "";
        } else {
            str4 = templateType;
        }
        tDEventUtils.n(k2.d.f96789i0, k2.e.f96825j, str, "part", B, k2.e.f96826k, k2.j.f96968d, "platform", k2.g.X0, "tags", str3, k2.e.f96837v, str4, "app_id", "9");
    }

    private final void eventReportSaveOrShare() {
        String str;
        TDEventUtils tDEventUtils = TDEventUtils.f33166a;
        BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo = this.mRelationBean;
        if (beanResourceRelationTemplateInfo == null || (str = beanResourceRelationTemplateInfo.itemCode()) == null) {
            str = "";
        }
        tDEventUtils.c(k2.d.R, k2.e.f96825j, str, k2.e.f96826k, k2.j.f96968d, "platform", k2.g.X0, "app_id", "9");
    }

    /* access modifiers changed from: private */
    public final void eventReportTaskSaveOrShareSuccess(String str) {
        String str2;
        TDEventUtils tDEventUtils = TDEventUtils.f33166a;
        BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo = this.mRelationBean;
        if (beanResourceRelationTemplateInfo == null || (str2 = beanResourceRelationTemplateInfo.itemCode()) == null) {
            str2 = "";
        }
        tDEventUtils.n(k2.d.f96785g0, "location", k2.g.f96897o1, "name", str, k2.e.f96825j, str2);
    }

    private final void eventReportUseProp() {
        String str;
        TDEventUtils tDEventUtils = TDEventUtils.f33166a;
        BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo = this.mRelationBean;
        if (beanResourceRelationTemplateInfo == null || (str = beanResourceRelationTemplateInfo.itemCode()) == null) {
            str = "";
        }
        tDEventUtils.n(k2.d.Q, k2.e.f96825j, str, k2.e.f96826k, k2.j.f96968d, "platform", k2.g.X0, "app_id", "9");
    }

    private final void eventReportUserProgress() {
        TDEventUtils tDEventUtils = TDEventUtils.f33166a;
        int q10 = tDEventUtils.q();
        if (q10 != 0) {
            com.gpower.coloringbynumber.spf.c cVar = com.gpower.coloringbynumber.spf.c.f33083b;
            if (cVar.H() != q10) {
                cVar.q1(q10);
                cVar.s1(0);
            }
            cVar.s1(cVar.J() + 1);
            tDEventUtils.o();
        }
        com.gpower.coloringbynumber.spf.c cVar2 = com.gpower.coloringbynumber.spf.c.f33083b;
        if (cVar2.I() < 4) {
            int T = cVar2.T();
            int I = cVar2.I();
            if (I == 1) {
                if (T == 3) {
                    tDEventUtils.p(k2.g.f96884k0);
                }
                if (T == 4) {
                    tDEventUtils.p(k2.g.f96887l0);
                }
                if (T == 5) {
                    tDEventUtils.p(k2.g.f96890m0);
                }
            } else if (I == 2) {
                if (T == 4) {
                    tDEventUtils.p(k2.g.f96893n0);
                }
                if (T == 5) {
                    tDEventUtils.p(k2.g.f96896o0);
                }
                if (T == 6) {
                    tDEventUtils.p(k2.g.f96899p0);
                }
            } else if (I == 3) {
                if (T == 5) {
                    tDEventUtils.p(k2.g.f96902q0);
                }
                if (T == 6) {
                    tDEventUtils.p(k2.g.f96905r0);
                }
                if (T == 7) {
                    tDEventUtils.p(k2.g.f96908s0);
                }
            }
        }
    }

    /* access modifiers changed from: private */
    public final void exitActivity() {
        if (!getMShowTrans() || (com.gpower.coloringbynumber.spf.c.f33083b.V() && this.curPaintStage == 3)) {
            showRefreshRunnable();
            finish();
            overridePendingTransition(R.anim.anim_no, R.anim.alpha_out_activity);
            return;
        }
        supportFinishAfterTransition();
    }

    private final d getMAdapterListener() {
        return (d) this.mAdapterListener$delegate.getValue();
    }

    private final float getMAspectRatio() {
        return ((Number) this.mAspectRatio$delegate.getValue()).floatValue();
    }

    private final int getMAutoPaintFindNum() {
        return ((Number) this.mAutoPaintFindNum$delegate.getValue(this, $$delegatedProperties[0])).intValue();
    }

    private final HobbyCollectionViewModelV25 getMCollectionViewModel() {
        return (HobbyCollectionViewModelV25) this.mCollectionViewModel$delegate.getValue();
    }

    private final ColorBaseViewModel getMColorViewModel() {
        return (ColorBaseViewModel) this.mColorViewModel$delegate.getValue();
    }

    private final int getMColoringPropsFindNum() {
        return ((Number) this.mColoringPropsFindNum$delegate.getValue(this, $$delegatedProperties[1])).intValue();
    }

    private final com.gpower.coloringbynumber.dialog.i getMDialogRemoveWaterMark() {
        return (com.gpower.coloringbynumber.dialog.i) this.mDialogRemoveWaterMark$delegate.getValue();
    }

    private final int getMGuessCover() {
        return ((Number) this.mGuessCover$delegate.getValue()).intValue();
    }

    private final String getMImageUrl() {
        return (String) this.mImageUrl$delegate.getValue();
    }

    private final LikeViewModel getMLikeViewModel() {
        return (LikeViewModel) this.mLikeViewModel$delegate.getValue();
    }

    private final String getMObjectKey() {
        return (String) this.mObjectKey$delegate.getValue();
    }

    private final com.gpower.coloringbynumber.pop.u getMPopSub() {
        return (com.gpower.coloringbynumber.pop.u) this.mPopSub$delegate.getValue();
    }

    /* access modifiers changed from: private */
    public final com.gpower.coloringbynumber.pop.k getMPopupClaimProp() {
        return (com.gpower.coloringbynumber.pop.k) this.mPopupClaimProp$delegate.getValue();
    }

    private final com.gpower.coloringbynumber.pop.p getMPopupPropsGift() {
        return (com.gpower.coloringbynumber.pop.p) this.mPopupPropsGift$delegate.getValue();
    }

    private final boolean getMRewardUnLock() {
        return ((Boolean) this.mRewardUnLock$delegate.getValue()).booleanValue();
    }

    /* access modifiers changed from: private */
    public final boolean getMShowAlpha() {
        return ((Boolean) this.mShowAlpha$delegate.getValue()).booleanValue();
    }

    /* access modifiers changed from: private */
    public final boolean getMShowTrans() {
        return ((Boolean) this.mShowTrans$delegate.getValue()).booleanValue();
    }

    private final String getMSvgPath() {
        return (String) this.mSvgPath$delegate.getValue();
    }

    private final void hideBannerAds() {
        FrameLayout frameLayout;
        try {
            Result.a aVar = Result.Companion;
            if (!this.remoteBannerControl && this.bannerShow) {
                this.bannerShow = false;
                ViewStubPaintToolsBinding viewStubPaintToolsBinding = this.mPaintToolsBinding;
                if (!(viewStubPaintToolsBinding == null || (frameLayout = viewStubPaintToolsBinding.bannerAdRl) == null)) {
                    frameLayout.setVisibility(4);
                }
                AdsActivityManager.f33023a.m();
                Result.m4962constructorimpl(Unit.f97091a);
            }
        } catch (Throwable th) {
            Result.a aVar2 = Result.Companion;
            Result.m4962constructorimpl(s0.a(th));
        }
    }

    private final void imageSettingRadio(float f10) {
        ConstraintLayout constraintLayout;
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding;
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding2;
        ConstraintSet constraintSet = new ConstraintSet();
        ActivityEditBinding activityEditBinding = this.mBinding;
        ConstraintLayout constraintLayout2 = null;
        if (activityEditBinding == null || (layoutPreviewLayoutBinding2 = activityEditBinding.editIncludePreviewLayout) == null) {
            constraintLayout = null;
        } else {
            constraintLayout = layoutPreviewLayoutBinding2.previewRootLayout;
        }
        constraintSet.clone(constraintLayout);
        constraintSet.setDimensionRatio(R.id.previewImageLayout, "h,1:" + f10);
        ActivityEditBinding activityEditBinding2 = this.mBinding;
        if (!(activityEditBinding2 == null || (layoutPreviewLayoutBinding = activityEditBinding2.editIncludePreviewLayout) == null)) {
            constraintLayout2 = layoutPreviewLayoutBinding.previewRootLayout;
        }
        constraintSet.applyTo(constraintLayout2);
    }

    /* access modifiers changed from: private */
    public static final Unit initData$lambda$37(ColoringActivity coloringActivity, BeanTemplateInfoDBM beanTemplateInfoDBM) {
        Intrinsics.m(beanTemplateInfoDBM);
        coloringActivity.dispatchLike(beanTemplateInfoDBM);
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit initData$lambda$38(ColoringActivity coloringActivity, V25BeanCategoryDBM v25BeanCategoryDBM) {
        Intrinsics.m(v25BeanCategoryDBM);
        coloringActivity.showHobbyView(v25BeanCategoryDBM);
        return Unit.f97091a;
    }

    private final void initInfoLoad() {
        if (com.gpower.coloringbynumber.spf.c.f33083b.U0()) {
            Object systemService = getSystemService("vibrator");
            Intrinsics.n(systemService, "null cannot be cast to non-null type android.os.Vibrator");
            this.mVibrator = (Vibrator) systemService;
        }
        this.mAdvPosition = "enter";
        this.swipeDrawAvailable = false;
        ge.c.f().v(this);
        this.isRewardLoaded = AdsActivityManager.f33023a.r();
        this.mStartPaintTime = System.currentTimeMillis();
    }

    /* access modifiers changed from: private */
    public final void initOrShowBannerAd() {
        y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(this), z0.c(), null, new ColoringActivity$initOrShowBannerAd$1(this, null), 2, null);
    }

    /* access modifiers changed from: private */
    public static final void initSuccessViewCreate$lambda$39(ColoringActivity coloringActivity, ViewStub viewStub, View view) {
        coloringActivity.getGoodsBoughtViewModel().queryGoodsBought(PurchaseUtil.GOODS_TYPE_ID_COUPON_HINT, PurchaseUtil.GOODS_TYPE_ID_COUPON_HINT);
    }

    /* access modifiers changed from: private */
    public static final void initSuccessViewCreate$lambda$42$lambda$41(ColoringActivity coloringActivity, View view, int i10, int i11, int i12, int i13) {
        refreshSelectColorAnim$default(coloringActivity, 0.0f, 1, null);
    }

    private final void insertHobbyCollection() {
        if (this.isHobbyCollection) {
            getMCollectionViewModel().getHobbyCollectFinishObserver().observe(this, new k(new Function1() { // from class: com.gpower.coloringbynumber.activity.y1
                @Override // kotlin.jvm.functions.Function1
                public final Object invoke(Object obj) {
                    return ColoringActivity.insertHobbyCollection$lambda$63((Triple) obj);
                }
            }));
            getMCollectionViewModel().requestRefreshHobbyCollected(this.categoryId, getMDataId(), this.categoryName);
        }
    }

    /* access modifiers changed from: private */
    public static final Unit insertHobbyCollection$lambda$63(Triple triple) {
        if (((Boolean) triple.getThird()).booleanValue()) {
            TDEventUtils.f33166a.d(k2.d.f96791j0, "location", triple.getSecond());
        }
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public final void judgeSubPop() {
        ActivityEditBinding activityEditBinding;
        ConstraintLayout root;
        com.gpower.coloringbynumber.spf.c cVar = com.gpower.coloringbynumber.spf.c.f33083b;
        if (!cVar.W0() && !this.firstWeekDiscountSubScribed && Intrinsics.g(this.abTestSubscriptionPage, o0.a.f99347a.b())) {
            int T = cVar.T();
            if ((T == 2 || T == 5 || T == 8 || T == 14 || T == 30) && (activityEditBinding = this.mBinding) != null && (root = activityEditBinding.getRoot()) != null) {
                getMPopSub().h(root);
            }
        }
    }

    /* access modifiers changed from: private */
    public final void lastOpenEditTime() {
        this.openEditTimeStamp = Long.valueOf(System.currentTimeMillis());
    }

    private final void listenerEvent() {
        App.I.b().q().observe(this, new k(new Function1() { // from class: com.gpower.coloringbynumber.activity.v0
            @Override // kotlin.jvm.functions.Function1
            public final Object invoke(Object obj) {
                return ColoringActivity.listenerEvent$lambda$34(ColoringActivity.this, (Boolean) obj);
            }
        }));
        getGoodsBoughtViewModel().getGoodsBoughtObserver().observe(this, new k(new Function1() { // from class: com.gpower.coloringbynumber.activity.g1
            @Override // kotlin.jvm.functions.Function1
            public final Object invoke(Object obj) {
                return ColoringActivity.listenerEvent$lambda$36$lambda$35(ColoringActivity.this, (Pair) obj);
            }
        }));
        dispatchPaintListenerEvent();
    }

    /* access modifiers changed from: private */
    public static final Unit listenerEvent$lambda$34(ColoringActivity coloringActivity, Boolean bool) {
        if (!coloringActivity.isBoughtCollectPackage) {
            Intrinsics.m(bool);
            coloringActivity.dispatchSubStatusChange(bool.booleanValue());
        } else {
            coloringActivity.dispatchSubStatusChange(true);
        }
        if (com.gpower.coloringbynumber.spf.c.f33083b.W0()) {
            coloringActivity.subscriptionSuccess();
        }
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit listenerEvent$lambda$36$lambda$35(ColoringActivity coloringActivity, Pair pair) {
        if (Intrinsics.g((String) pair.getFirst(), PurchaseUtil.GOODS_TYPE_ID_COUPON_HINT)) {
            int goodsNum = ((GoodsBoughtBean) pair.getSecond()).getGoodsNum();
            com.gpower.coloringbynumber.spf.c cVar = com.gpower.coloringbynumber.spf.c.f33083b;
            coloringActivity.setMColoringPropsFindNum(goodsNum + cVar.n0() + cVar.F0());
        }
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public final void loadColorData() {
        y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(this), z0.c(), null, new ColoringActivity$loadColorData$1(this, null), 2, null);
    }

    /* access modifiers changed from: private */
    public final void loadData() {
        String mDataId;
        String mSvgPath = getMSvgPath();
        if ((mSvgPath == null || mSvgPath.length() == 0) && ((mDataId = getMDataId()) == null || mDataId.length() == 0)) {
            showErrorView();
            return;
        }
        showInterstitialAdvAndTD(new Function1() { // from class: com.gpower.coloringbynumber.activity.u0
            @Override // kotlin.jvm.functions.Function1
            public final Object invoke(Object obj) {
                return ColoringActivity.loadData$lambda$26(((Boolean) obj).booleanValue());
            }
        });
        y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(this), z0.c(), null, new ColoringActivity$loadData$2(this, null), 2, null);
        TDEventUtils.f33166a.n(k2.d.f96801o0, new Object[0]);
        String mDataId2 = getMDataId();
        if (mDataId2 != null) {
            dispatchLoadTemplateInfo(mDataId2, getMV25Data());
        }
    }

    /* access modifiers changed from: private */
    public static final Unit loadData$lambda$26(boolean z10) {
        AdsActivityManager.f33023a.o(k2.f.f96851j);
        return Unit.f97091a;
    }

    private final void loadPreView(boolean z10) {
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding;
        ConstraintLayout constraintLayout;
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding2;
        CustomPreviewImageView customPreviewImageView;
        int i10;
        getWindow().setBackgroundDrawable(new ColorDrawable(-1));
        if (Build.VERSION.SDK_INT >= 30) {
            o.a(this, false);
        }
        listenerEvent();
        int d10 = h0.c.f88264a.d() - (com.base.module.tools.model_base.tools.m.b(73.0f) * 2);
        int mAspectRatio = (int) (((float) d10) * getMAspectRatio());
        if (z10) {
            Object mImageUrl = getMImageUrl();
            if (mImageUrl != null) {
                ActivityEditBinding activityEditBinding = this.mBinding;
                if (!(activityEditBinding == null || (layoutPreviewLayoutBinding2 = activityEditBinding.editIncludePreviewLayout) == null || (customPreviewImageView = layoutPreviewLayoutBinding2.previewImage) == null)) {
                    RequestManager with = Glide.with((FragmentActivity) this);
                    if (getMGuessCover() != 0) {
                        mImageUrl = Integer.valueOf(getMGuessCover());
                    }
                    RequestBuilder override = with.load(mImageUrl).override(d10, mAspectRatio);
                    Object mObjectKey = getMObjectKey();
                    if (mObjectKey == null) {
                        mObjectKey = Long.valueOf(System.currentTimeMillis());
                    }
                    RequestBuilder signature = override.signature(new ObjectKey(mObjectKey));
                    if (getMAspectRatio() == 1.0f) {
                        i10 = R.drawable.ic_placeholder_rounded;
                    } else {
                        i10 = R.drawable.ic_placeholder_rounded_large;
                    }
                    signature.placeholder(i10).transition(DrawableTransitionOptions.withCrossFade(500)).transform(new CenterCrop(), new RoundedCorners(com.base.module.tools.model_base.tools.m.b(12.0f))).into(customPreviewImageView);
                }
                imageSettingRadio(getMAspectRatio());
            } else {
                ActivityEditBinding activityEditBinding2 = this.mBinding;
                if (!(activityEditBinding2 == null || (layoutPreviewLayoutBinding = activityEditBinding2.editIncludePreviewLayout) == null || (constraintLayout = layoutPreviewLayoutBinding.previewRootLayout) == null)) {
                    constraintLayout.setVisibility(0);
                }
            }
        }
        previewLoadingTvStyle();
    }

    public static /* synthetic */ void loadPreView$default(ColoringActivity coloringActivity, boolean z10, int i10, Object obj) {
        if (obj == null) {
            if ((i10 & 1) != 0) {
                z10 = !coloringActivity.getMShowTrans();
            }
            coloringActivity.loadPreView(z10);
            return;
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: loadPreView");
    }

    /* access modifiers changed from: private */
    public static final d mAdapterListener_delegate$lambda$3(ColoringActivity coloringActivity) {
        return new d(coloringActivity);
    }

    /* access modifiers changed from: private */
    public static final float mAspectRatio_delegate$lambda$8(ColoringActivity coloringActivity) {
        return coloringActivity.getIntent().getFloatExtra(k2.i.f96948d, 1.0f);
    }

    /* access modifiers changed from: private */
    public static final PaintColorListAdapter mColorListAdapter_delegate$lambda$4(ColoringActivity coloringActivity) {
        return new PaintColorListAdapter(coloringActivity.getMAdapterListener());
    }

    /* access modifiers changed from: private */
    public static final String mDataId_delegate$lambda$13(ColoringActivity coloringActivity) {
        return coloringActivity.getIntent().getStringExtra(k2.i.f96950f);
    }

    /* access modifiers changed from: private */
    public static final com.gpower.coloringbynumber.dialog.i mDialogRemoveWaterMark_delegate$lambda$0(ColoringActivity coloringActivity) {
        return new com.gpower.coloringbynumber.dialog.i(coloringActivity, new f(coloringActivity));
    }

    /* access modifiers changed from: private */
    public static final int mGuessCover_delegate$lambda$10(ColoringActivity coloringActivity) {
        return coloringActivity.getIntent().getIntExtra(k2.i.f96963s, 0);
    }

    /* access modifiers changed from: private */
    public static final String mImageUrl_delegate$lambda$6(ColoringActivity coloringActivity) {
        return coloringActivity.getIntent().getStringExtra("image_url");
    }

    /* access modifiers changed from: private */
    public static final String mObjectKey_delegate$lambda$9(ColoringActivity coloringActivity) {
        return coloringActivity.getIntent().getStringExtra(k2.i.f96949e);
    }

    /* access modifiers changed from: private */
    public static final void mPermissionLauncher$lambda$18(ColoringActivity coloringActivity, Map map) {
        Intrinsics.checkNotNullParameter(map, "it");
        for (Boolean bool : map.values()) {
            if (!bool.booleanValue()) {
                if (Build.VERSION.SDK_INT >= 33) {
                    if (ActivityCompat.shouldShowRequestPermissionRationale(coloringActivity, "android.permission.READ_MEDIA_IMAGES") && ActivityCompat.shouldShowRequestPermissionRationale(coloringActivity, "android.permission.READ_MEDIA_VIDEO")) {
                        return;
                    }
                } else if (ActivityCompat.shouldShowRequestPermissionRationale(coloringActivity, "android.permission.READ_EXTERNAL_STORAGE") && ActivityCompat.shouldShowRequestPermissionRationale(coloringActivity, "android.permission.WRITE_EXTERNAL_STORAGE")) {
                    return;
                }
                new AlertDialog.Builder(coloringActivity).setTitle(coloringActivity.getString(R.string.permission_request_title)).setMessage(coloringActivity.getString(R.string.permission_request_content)).setNegativeButton(coloringActivity.getString(R.string.dialog_cancel), new DialogInterface.OnClickListener() { // from class: com.gpower.coloringbynumber.activity.a1
                    @Override // android.content.DialogInterface.OnClickListener
                    public final void onClick(DialogInterface dialogInterface, int i10) {
                        ColoringActivity.mPermissionLauncher$lambda$18$lambda$16(ColoringActivity.this, dialogInterface, i10);
                    }
                }).setPositiveButton(coloringActivity.getString(R.string.permission_request_confirm), new DialogInterface.OnClickListener() { // from class: com.gpower.coloringbynumber.activity.b1
                    @Override // android.content.DialogInterface.OnClickListener
                    public final void onClick(DialogInterface dialogInterface, int i10) {
                        ColoringActivity.mPermissionLauncher$lambda$18$lambda$17(ColoringActivity.this, dialogInterface, i10);
                    }
                }).create().show();
                return;
            }
        }
        coloringActivity.requestPermissionFinish();
    }

    /* access modifiers changed from: private */
    public static final void mPermissionLauncher$lambda$18$lambda$16(ColoringActivity coloringActivity, DialogInterface dialogInterface, int i10) {
        dialogInterface.dismiss();
        coloringActivity.permissionRequestFail();
    }

    /* access modifiers changed from: private */
    public static final void mPermissionLauncher$lambda$18$lambda$17(ColoringActivity coloringActivity, DialogInterface dialogInterface, int i10) {
        coloringActivity.openSettings();
    }

    /* access modifiers changed from: private */
    public static final com.gpower.coloringbynumber.pop.u mPopSub_delegate$lambda$2(ColoringActivity coloringActivity) {
        return new com.gpower.coloringbynumber.pop.u(coloringActivity, new Function0() { // from class: com.gpower.coloringbynumber.activity.w0
            @Override // kotlin.jvm.functions.Function0
            public final Object invoke() {
                return ColoringActivity.mPopSub_delegate$lambda$2$lambda$1(ColoringActivity.this);
            }
        });
    }

    /* access modifiers changed from: private */
    public static final Unit mPopSub_delegate$lambda$2$lambda$1(ColoringActivity coloringActivity) {
        coloringActivity.showTd = true;
        BaseActivity.startSub$default(coloringActivity, "week_new", null, null, 6, null);
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final com.gpower.coloringbynumber.pop.k mPopupClaimProp_delegate$lambda$136(ColoringActivity coloringActivity) {
        return new com.gpower.coloringbynumber.pop.k(coloringActivity, new g(coloringActivity));
    }

    /* access modifiers changed from: private */
    public static final com.gpower.coloringbynumber.pop.p mPopupPropsGift_delegate$lambda$135(ColoringActivity coloringActivity) {
        return new com.gpower.coloringbynumber.pop.p(coloringActivity, new h(coloringActivity));
    }

    /* access modifiers changed from: private */
    public static final PopSelectColor mPopupSelectColor_delegate$lambda$15(ColoringActivity coloringActivity) {
        return new PopSelectColor(coloringActivity);
    }

    /* access modifiers changed from: private */
    public static final boolean mRewardUnLock_delegate$lambda$11(ColoringActivity coloringActivity) {
        return coloringActivity.getIntent().getBooleanExtra(k2.i.f96964t, false);
    }

    /* access modifiers changed from: private */
    public static final boolean mShowAlpha_delegate$lambda$12(ColoringActivity coloringActivity) {
        return coloringActivity.getIntent().getBooleanExtra(k2.i.f96954j, false);
    }

    /* access modifiers changed from: private */
    public static final boolean mShowTrans_delegate$lambda$7(ColoringActivity coloringActivity) {
        return coloringActivity.getIntent().getBooleanExtra(k2.i.f96953i, false);
    }

    /* access modifiers changed from: private */
    public static final String mSvgPath_delegate$lambda$5(ColoringActivity coloringActivity) {
        return coloringActivity.getIntent().getStringExtra(k2.i.f96946b);
    }

    /* access modifiers changed from: private */
    public static final boolean mV25Data_delegate$lambda$14(ColoringActivity coloringActivity) {
        return coloringActivity.getIntent().getBooleanExtra(k2.i.f96951g, false);
    }

    /* access modifiers changed from: private */
    public static final Unit onClick$lambda$101() {
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final void onClick$lambda$104(ColoringActivity coloringActivity) {
        ViewModelAchievement i10 = App.I.b().i();
        if (i10 != null) {
            i10.achievementTypeAddProgress(5);
        }
        String string = coloringActivity.getString(R.string.coloring_download);
        Intrinsics.checkNotNullExpressionValue(string, "getString(...)");
        coloringActivity.showDownloadPop(string, new Function1() { // from class: com.gpower.coloringbynumber.activity.c1
            @Override // kotlin.jvm.functions.Function1
            public final Object invoke(Object obj) {
                return ColoringActivity.onClick$lambda$104$lambda$102(ColoringActivity.this, (String) obj);
            }
        }, new Function1() { // from class: com.gpower.coloringbynumber.activity.d1
            @Override // kotlin.jvm.functions.Function1
            public final Object invoke(Object obj) {
                return ColoringActivity.onClick$lambda$104$lambda$103(ColoringActivity.this, (String) obj);
            }
        });
    }

    /* access modifiers changed from: private */
    public static final Unit onClick$lambda$104$lambda$102(ColoringActivity coloringActivity, String str) {
        Intrinsics.checkNotNullParameter(str, "it");
        y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(coloringActivity), z0.c(), null, new ColoringActivity$onClick$9$1$1(str, coloringActivity, null), 2, null);
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit onClick$lambda$104$lambda$103(ColoringActivity coloringActivity, String str) {
        Intrinsics.checkNotNullParameter(str, "it");
        y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(coloringActivity), z0.c(), null, new ColoringActivity$onClick$9$2$1(str, coloringActivity, null), 2, null);
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit onClick$lambda$105(ColoringActivity coloringActivity) {
        Runnable runnable = coloringActivity.mSaveRunnable;
        if (runnable != null) {
            runnable.run();
        }
        coloringActivity.mSaveRunnable = null;
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final void onClick$lambda$109(ColoringActivity coloringActivity) {
        ViewModelAchievement i10 = App.I.b().i();
        if (i10 != null) {
            i10.achievementTypeAddProgress(4);
        }
        String string = coloringActivity.getString(R.string.dialog_share);
        Intrinsics.checkNotNullExpressionValue(string, "getString(...)");
        coloringActivity.showDownloadPop(string, new Function1() { // from class: com.gpower.coloringbynumber.activity.u
            @Override // kotlin.jvm.functions.Function1
            public final Object invoke(Object obj) {
                return ColoringActivity.onClick$lambda$109$lambda$107(ColoringActivity.this, (String) obj);
            }
        }, new Function1() { // from class: com.gpower.coloringbynumber.activity.v
            @Override // kotlin.jvm.functions.Function1
            public final Object invoke(Object obj) {
                return ColoringActivity.onClick$lambda$109$lambda$108(ColoringActivity.this, (String) obj);
            }
        });
    }

    /* access modifiers changed from: private */
    public static final Unit onClick$lambda$109$lambda$107(ColoringActivity coloringActivity, String str) {
        Intrinsics.checkNotNullParameter(str, "it");
        y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(coloringActivity), z0.c(), null, new ColoringActivity$onClick$12$1$1(str, coloringActivity, null), 2, null);
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit onClick$lambda$109$lambda$108(ColoringActivity coloringActivity, String str) {
        Intrinsics.checkNotNullParameter(str, "it");
        y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(coloringActivity), z0.c(), null, new ColoringActivity$onClick$12$2$1(str, coloringActivity, null), 2, null);
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit onClick$lambda$110(ColoringActivity coloringActivity) {
        Runnable runnable = coloringActivity.mShareRunnable;
        if (runnable != null) {
            runnable.run();
        }
        coloringActivity.mSaveRunnable = null;
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit onClick$lambda$85$lambda$78(ColoringActivity coloringActivity, boolean z10) {
        coloringActivity.exitActivity();
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit onClick$lambda$85$lambda$84(ColoringActivity coloringActivity, Bitmap bitmap) {
        coloringActivity.showInterstitialAdvAndTD(new Function1(bitmap) { // from class: com.gpower.coloringbynumber.activity.t

            /* renamed from: u  reason: collision with root package name */
            public final /* synthetic */ Bitmap f32230u;

            {
                this.f32230u = r2;
            }

            @Override // kotlin.jvm.functions.Function1
            public final Object invoke(Object obj) {
                return ColoringActivity.onClick$lambda$85$lambda$84$lambda$83(ColoringActivity.this, this.f32230u, ((Boolean) obj).booleanValue());
            }
        });
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit onClick$lambda$85$lambda$84$lambda$83(ColoringActivity coloringActivity, Bitmap bitmap, boolean z10) {
        Object obj;
        ImageView imageView;
        ImageView imageView2;
        ImageView imageView3;
        ImageView imageView4;
        ImageView imageView5;
        try {
            Result.a aVar = Result.Companion;
            ActivityEditBinding activityEditBinding = coloringActivity.mBinding;
            if (!(activityEditBinding == null || (imageView5 = activityEditBinding.editPathExitFinishView) == null)) {
                imageView5.setImageBitmap(bitmap);
            }
            ViewStubFinishViewBinding viewStubFinishViewBinding = coloringActivity.mFinishResultBinding;
            if (!(viewStubFinishViewBinding == null || (imageView4 = viewStubFinishViewBinding.editPathWaterMaskIcon) == null)) {
                imageView4.setVisibility(8);
            }
            ViewStubFinishViewBinding viewStubFinishViewBinding2 = coloringActivity.mFinishResultBinding;
            if (!(viewStubFinishViewBinding2 == null || (imageView3 = viewStubFinishViewBinding2.editPathWaterMaskCloseIcon) == null)) {
                imageView3.setVisibility(8);
            }
            ActivityEditBinding activityEditBinding2 = coloringActivity.mBinding;
            if (!(activityEditBinding2 == null || (imageView2 = activityEditBinding2.editPathExitFinishView) == null)) {
                imageView2.setVisibility(0);
            }
            ActivityEditBinding activityEditBinding3 = coloringActivity.mBinding;
            if (activityEditBinding3 == null || (imageView = activityEditBinding3.editPathExitFinishView) == null) {
                coloringActivity.exitActivity();
            } else {
                coloringActivity.showAlphaView(imageView, new Function0() { // from class: com.gpower.coloringbynumber.activity.w1
                    @Override // kotlin.jvm.functions.Function0
                    public final Object invoke() {
                        return ColoringActivity.onClick$lambda$85$lambda$84$lambda$83$lambda$81$lambda$80$lambda$79(ColoringActivity.this);
                    }
                });
            }
            obj = Result.m4962constructorimpl(Unit.f97091a);
        } catch (Throwable th) {
            Result.a aVar2 = Result.Companion;
            obj = Result.m4962constructorimpl(s0.a(th));
        }
        Throwable r22 = Result.m4965exceptionOrNullimpl(obj);
        if (r22 != null) {
            String message = r22.getMessage();
            com.base.module.tools.model_base.tools.p.a(o0.c.f99366f, "Glide load failure onClick " + message);
            coloringActivity.exitActivity();
        }
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit onClick$lambda$85$lambda$84$lambda$83$lambda$81$lambda$80$lambda$79(ColoringActivity coloringActivity) {
        View view;
        ActivityEditBinding activityEditBinding = coloringActivity.mBinding;
        if (!(activityEditBinding == null || (view = activityEditBinding.editPathExitBgView) == null)) {
            view.setVisibility(0);
        }
        coloringActivity.exitActivity();
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit onClick$lambda$97$lambda$88(ColoringActivity coloringActivity, boolean z10) {
        coloringActivity.exitActivity();
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit onClick$lambda$97$lambda$96(ColoringActivity coloringActivity) {
        coloringActivity.showInterstitialAdvAndTD(new Function1() { // from class: com.gpower.coloringbynumber.activity.x0
            @Override // kotlin.jvm.functions.Function1
            public final Object invoke(Object obj) {
                return ColoringActivity.onClick$lambda$97$lambda$96$lambda$95(ColoringActivity.this, ((Boolean) obj).booleanValue());
            }
        });
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit onClick$lambda$97$lambda$96$lambda$95(ColoringActivity coloringActivity, boolean z10) {
        coloringActivity.dispatchCurProgressPaintBitmap(new Function1() { // from class: com.gpower.coloringbynumber.activity.r
            @Override // kotlin.jvm.functions.Function1
            public final Object invoke(Object obj) {
                return ColoringActivity.onClick$lambda$97$lambda$96$lambda$95$lambda$94(ColoringActivity.this, (Bitmap) obj);
            }
        });
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit onClick$lambda$97$lambda$96$lambda$95$lambda$94(ColoringActivity coloringActivity, Bitmap bitmap) {
        ImageView imageView;
        Object obj;
        ImageView imageView2;
        ImageView imageView3;
        ActivityEditBinding activityEditBinding = coloringActivity.mBinding;
        if (!(activityEditBinding == null || (imageView = activityEditBinding.editPathExitFinishView) == null)) {
            try {
                Result.a aVar = Result.Companion;
                if (!(activityEditBinding == null || imageView == null)) {
                    imageView.setImageBitmap(bitmap);
                }
                ActivityEditBinding activityEditBinding2 = coloringActivity.mBinding;
                if (!(activityEditBinding2 == null || (imageView3 = activityEditBinding2.editPathExitFinishView) == null)) {
                    imageView3.setVisibility(0);
                }
                ActivityEditBinding activityEditBinding3 = coloringActivity.mBinding;
                if (activityEditBinding3 == null || (imageView2 = activityEditBinding3.editPathExitFinishView) == null) {
                    coloringActivity.exitActivity();
                } else {
                    coloringActivity.showAlphaView(imageView2, new Function0() { // from class: com.gpower.coloringbynumber.activity.j1
                        @Override // kotlin.jvm.functions.Function0
                        public final Object invoke() {
                            return ColoringActivity.onClick$lambda$97$lambda$96$lambda$95$lambda$94$lambda$93$lambda$91$lambda$90$lambda$89(ColoringActivity.this);
                        }
                    });
                }
                obj = Result.m4962constructorimpl(Unit.f97091a);
            } catch (Throwable th) {
                Result.a aVar2 = Result.Companion;
                obj = Result.m4962constructorimpl(s0.a(th));
            }
            Throwable r42 = Result.m4965exceptionOrNullimpl(obj);
            if (r42 != null) {
                String message = r42.getMessage();
                com.base.module.tools.model_base.tools.p.a(o0.c.f99366f, "Glide load failure onClick " + message);
                coloringActivity.exitActivity();
            }
        }
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit onClick$lambda$97$lambda$96$lambda$95$lambda$94$lambda$93$lambda$91$lambda$90$lambda$89(ColoringActivity coloringActivity) {
        View view;
        coloringActivity.dispatchHidePaintView();
        ActivityEditBinding activityEditBinding = coloringActivity.mBinding;
        if (!(activityEditBinding == null || (view = activityEditBinding.editPathExitBgView) == null)) {
            view.setVisibility(0);
        }
        coloringActivity.exitActivity();
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public final void onLongPress(int i10) {
        startVibrator();
        TDEventUtils.f33166a.n(k2.d.f96781e0, new Object[0]);
        this.propsUnUse = false;
        getMColorListAdapter().updateIndex(i10);
    }

    private final void openSettings() {
        Intent intent = new Intent();
        intent.setAction("android.settings.APPLICATION_DETAILS_SETTINGS");
        String packageName = getPackageName();
        intent.setData(Uri.parse("package:" + packageName));
        startActivity(intent);
    }

    /* access modifiers changed from: private */
    public final void pathProViewInitFinish(RectF rectF) {
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding;
        ConstraintLayout constraintLayout;
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding2;
        ConstraintLayout constraintLayout2;
        ActivityEditBinding activityEditBinding = this.mBinding;
        if (!(activityEditBinding == null || (layoutPreviewLayoutBinding2 = activityEditBinding.editIncludePreviewLayout) == null || (constraintLayout2 = layoutPreviewLayoutBinding2.previewTvLayout) == null)) {
            constraintLayout2.setVisibility(4);
        }
        this.loading = false;
        ActivityEditBinding activityEditBinding2 = this.mBinding;
        if (activityEditBinding2 != null && (layoutPreviewLayoutBinding = activityEditBinding2.editIncludePreviewLayout) != null && (constraintLayout = layoutPreviewLayoutBinding.previewImageLayout) != null) {
            constraintLayout.post(new Runnable(rectF) { // from class: com.gpower.coloringbynumber.activity.w

                /* renamed from: u  reason: collision with root package name */
                public final /* synthetic */ RectF f32248u;

                {
                    this.f32248u = r2;
                }

                @Override // java.lang.Runnable
                public final void run() {
                    ColoringActivity.pathProViewInitFinish$lambda$74(ColoringActivity.this, this.f32248u);
                }
            });
        }
    }

    /* access modifiers changed from: private */
    public static final void pathProViewInitFinish$lambda$74(ColoringActivity coloringActivity, RectF rectF) {
        float f10;
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding;
        ConstraintLayout constraintLayout;
        ConstraintLayout constraintLayout2;
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding2;
        FrameLayout frameLayout;
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding3;
        ConstraintLayout constraintLayout3;
        ActivityEditBinding activityEditBinding = coloringActivity.mBinding;
        if (!(activityEditBinding == null || (layoutPreviewLayoutBinding3 = activityEditBinding.editIncludePreviewLayout) == null || (constraintLayout3 = layoutPreviewLayoutBinding3.previewImageLayout) == null)) {
            coloringActivity.mPreRectF.set((float) constraintLayout3.getLeft(), (float) constraintLayout3.getTop(), (float) constraintLayout3.getRight(), (float) constraintLayout3.getBottom());
        }
        ActivityEditBinding activityEditBinding2 = coloringActivity.mBinding;
        if (!(activityEditBinding2 == null || (frameLayout = activityEditBinding2.editFinishAnimLayout) == null)) {
            ViewGroup.LayoutParams layoutParams = frameLayout.getLayoutParams();
            if (layoutParams != null) {
                ConstraintLayout.LayoutParams layoutParams2 = (ConstraintLayout.LayoutParams) layoutParams;
                ((ViewGroup.MarginLayoutParams) layoutParams2).width = ((int) rectF.width()) + com.base.module.tools.model_base.tools.m.b(4.0f);
                ((ViewGroup.MarginLayoutParams) layoutParams2).height = ((int) rectF.height()) + com.base.module.tools.model_base.tools.m.b(4.0f);
                ((ViewGroup.MarginLayoutParams) layoutParams2).topMargin = ((int) rectF.top) - com.base.module.tools.model_base.tools.m.b(2.0f);
                layoutParams2.setMarginStart(((int) rectF.left) - com.base.module.tools.model_base.tools.m.b(2.0f));
                frameLayout.setLayoutParams(layoutParams2);
            } else {
                throw new NullPointerException("null cannot be cast to non-null type androidx.constraintlayout.widget.ConstraintLayout.LayoutParams");
            }
        }
        float centerX = rectF.centerX() - coloringActivity.mPreRectF.centerX();
        float centerY = rectF.centerY() - coloringActivity.mPreRectF.centerY();
        if (coloringActivity.mPreRectF.isEmpty() || coloringActivity.mPreRectF.width() <= 0.0f || coloringActivity.mPreRectF.height() <= 0.0f) {
            f10 = 1.0f;
        } else {
            f10 = Math.max(rectF.width() / coloringActivity.mPreRectF.width(), rectF.height() / coloringActivity.mPreRectF.height());
        }
        ActivityEditBinding activityEditBinding3 = coloringActivity.mBinding;
        if (activityEditBinding3 != null && (layoutPreviewLayoutBinding = activityEditBinding3.editIncludePreviewLayout) != null && (constraintLayout = layoutPreviewLayoutBinding.previewImageLayout) != null) {
            if (Float.isNaN(f10)) {
                f10 = 1.0f;
            }
            ObjectAnimator ofFloat = ObjectAnimator.ofFloat(constraintLayout, "scaleX", 1.0f, f10);
            ObjectAnimator ofFloat2 = ObjectAnimator.ofFloat(constraintLayout, "scaleY", 1.0f, f10);
            ObjectAnimator ofFloat3 = ObjectAnimator.ofFloat(constraintLayout, "translationX", 0.0f, centerX);
            ObjectAnimator ofFloat4 = ObjectAnimator.ofFloat(constraintLayout, "translationY", 0.0f, centerY);
            ActivityEditBinding activityEditBinding4 = coloringActivity.mBinding;
            if (activityEditBinding4 == null || (layoutPreviewLayoutBinding2 = activityEditBinding4.editIncludePreviewLayout) == null) {
                constraintLayout2 = null;
            } else {
                constraintLayout2 = layoutPreviewLayoutBinding2.previewRootLayout;
            }
            ObjectAnimator ofFloat5 = ObjectAnimator.ofFloat(constraintLayout2, "alpha", 1.0f, 0.0f);
            AnimatorSet animatorSet = new AnimatorSet();
            animatorSet.playTogether(ofFloat, ofFloat2, ofFloat3, ofFloat4);
            animatorSet.addListener(new i(coloringActivity, ofFloat5));
            animatorSet.setDuration(1000L);
            if (!coloringActivity.clickExit) {
                animatorSet.start();
            } else {
                coloringActivity.mAnimSet = null;
            }
            coloringActivity.mAnimSet = animatorSet;
        }
    }

    private final void permissionRequestFail() {
        this.mSaveRunnable = null;
        this.mShareRunnable = null;
    }

    /* access modifiers changed from: private */
    /* JADX WARNING: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARNING: Removed duplicated region for block: B:19:0x0051  */
    /* JADX WARNING: Removed duplicated region for block: B:29:0x009c  */
    /* JADX WARNING: Removed duplicated region for block: B:35:0x00b7  */
    /* JADX WARNING: Removed duplicated region for block: B:36:0x00bb  */
    /* JADX WARNING: Removed duplicated region for block: B:39:0x00d0 A[RETURN] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object previewLoadingStyleAbTestB(kotlin.coroutines.Continuation<? super kotlin.Unit> r10) {
        /*
        // Method dump skipped, instructions count: 212
        */
        throw new UnsupportedOperationException("Method not decompiled: com.gpower.coloringbynumber.activity.ColoringActivity.previewLoadingStyleAbTestB(kotlin.coroutines.Continuation):java.lang.Object");
    }

    /* access modifiers changed from: private */
    /* JADX WARNING: Removed duplicated region for block: B:10:0x0024  */
    /* JADX WARNING: Removed duplicated region for block: B:14:0x0032  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object previewLoadingStyleAbTestD(kotlin.coroutines.Continuation<? super kotlin.Unit> r11) {
        /*
            r10 = this;
            boolean r0 = r11 instanceof com.gpower.coloringbynumber.activity.ColoringActivity$previewLoadingStyleAbTestD$1
            if (r0 == 0) goto L_0x0013
            r0 = r11
            com.gpower.coloringbynumber.activity.ColoringActivity$previewLoadingStyleAbTestD$1 r0 = (com.gpower.coloringbynumber.activity.ColoringActivity$previewLoadingStyleAbTestD$1) r0
            int r1 = r0.label
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.label = r1
            goto L_0x0018
        L_0x0013:
            com.gpower.coloringbynumber.activity.ColoringActivity$previewLoadingStyleAbTestD$1 r0 = new com.gpower.coloringbynumber.activity.ColoringActivity$previewLoadingStyleAbTestD$1
            r0.<init>(r10, r11)
        L_0x0018:
            java.lang.Object r11 = r0.result
            java.lang.Object r1 = kotlin.coroutines.intrinsics.b.l()
            int r2 = r0.label
            r3 = 0
            r4 = 1
            if (r2 == 0) goto L_0x0032
            if (r2 != r4) goto L_0x002a
            kotlin.s0.n(r11)
            goto L_0x0047
        L_0x002a:
            java.lang.IllegalStateException r11 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r11.<init>(r0)
            throw r11
        L_0x0032:
            kotlin.s0.n(r11)
            kotlinx.coroutines.h2 r11 = kotlinx.coroutines.z0.e()
            com.gpower.coloringbynumber.activity.ColoringActivity$previewLoadingStyleAbTestD$2 r2 = new com.gpower.coloringbynumber.activity.ColoringActivity$previewLoadingStyleAbTestD$2
            r2.<init>(r10, r3)
            r0.label = r4
            java.lang.Object r11 = kotlinx.coroutines.h.h(r11, r2, r0)
            if (r11 != r1) goto L_0x0047
            return r1
        L_0x0047:
            androidx.lifecycle.LifecycleCoroutineScope r4 = androidx.lifecycle.LifecycleOwnerKt.getLifecycleScope(r10)
            kotlinx.coroutines.CoroutineDispatcher r5 = kotlinx.coroutines.z0.c()
            com.gpower.coloringbynumber.activity.ColoringActivity$previewLoadingStyleAbTestD$3 r7 = new com.gpower.coloringbynumber.activity.ColoringActivity$previewLoadingStyleAbTestD$3
            r7.<init>(r10, r3)
            r8 = 2
            r9 = 0
            r6 = 0
            kotlinx.coroutines.h.e(r4, r5, r6, r7, r8, r9)
            kotlin.Unit r11 = kotlin.Unit.f97091a
            return r11
        */
        throw new UnsupportedOperationException("Method not decompiled: com.gpower.coloringbynumber.activity.ColoringActivity.previewLoadingStyleAbTestD(kotlin.coroutines.Continuation):java.lang.Object");
    }

    /* access modifiers changed from: private */
    /* JADX WARNING: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARNING: Removed duplicated region for block: B:19:0x0049  */
    /* JADX WARNING: Removed duplicated region for block: B:25:0x0090  */
    /* JADX WARNING: Removed duplicated region for block: B:31:0x00ac  */
    /* JADX WARNING: Removed duplicated region for block: B:34:0x00bb A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:35:0x00bc  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object previewLoadingStyleDefault(kotlin.coroutines.Continuation<? super kotlin.Unit> r10) {
        /*
            r9 = this;
            boolean r0 = r10 instanceof com.gpower.coloringbynumber.activity.ColoringActivity$previewLoadingStyleDefault$1
            if (r0 == 0) goto L_0x0013
            r0 = r10
            com.gpower.coloringbynumber.activity.ColoringActivity$previewLoadingStyleDefault$1 r0 = (com.gpower.coloringbynumber.activity.ColoringActivity$previewLoadingStyleDefault$1) r0
            int r1 = r0.label
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.label = r1
            goto L_0x0018
        L_0x0013:
            com.gpower.coloringbynumber.activity.ColoringActivity$previewLoadingStyleDefault$1 r0 = new com.gpower.coloringbynumber.activity.ColoringActivity$previewLoadingStyleDefault$1
            r0.<init>(r9, r10)
        L_0x0018:
            java.lang.Object r10 = r0.result
            java.lang.Object r1 = kotlin.coroutines.intrinsics.b.l()
            int r2 = r0.label
            r3 = 0
            r4 = 2
            r5 = 3
            r6 = 1
            if (r2 == 0) goto L_0x0049
            if (r2 == r6) goto L_0x0045
            if (r2 == r4) goto L_0x003d
            if (r2 != r5) goto L_0x0035
            java.lang.Object r2 = r0.L$0
            java.util.List r2 = (java.util.List) r2
            kotlin.s0.n(r10)
        L_0x0033:
            r10 = r2
            goto L_0x008c
        L_0x0035:
            java.lang.IllegalStateException r10 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r10.<init>(r0)
            throw r10
        L_0x003d:
            java.lang.Object r2 = r0.L$0
            java.util.List r2 = (java.util.List) r2
            kotlin.s0.n(r10)
            goto L_0x00a5
        L_0x0045:
            kotlin.s0.n(r10)
            goto L_0x005e
        L_0x0049:
            kotlin.s0.n(r10)
            kotlinx.coroutines.h2 r10 = kotlinx.coroutines.z0.e()
            com.gpower.coloringbynumber.activity.ColoringActivity$previewLoadingStyleDefault$2 r2 = new com.gpower.coloringbynumber.activity.ColoringActivity$previewLoadingStyleDefault$2
            r2.<init>(r9, r3)
            r0.label = r6
            java.lang.Object r10 = kotlinx.coroutines.h.h(r10, r2, r0)
            if (r10 != r1) goto L_0x005e
            return r1
        L_0x005e:
            java.util.ArrayList r10 = new java.util.ArrayList
            r10.<init>()
            int r2 = com.color.by.number.paint.ly.pixel.art.R.string.loading_tips_1
            java.lang.String r2 = r9.getString(r2)
            java.lang.String r7 = "getString(...)"
            kotlin.jvm.internal.Intrinsics.checkNotNullExpressionValue(r2, r7)
            r10.add(r2)
            int r2 = com.color.by.number.paint.ly.pixel.art.R.string.loading_tips_2
            java.lang.String r2 = r9.getString(r2)
            kotlin.jvm.internal.Intrinsics.checkNotNullExpressionValue(r2, r7)
            r10.add(r2)
            int r2 = com.color.by.number.paint.ly.pixel.art.R.string.loading_tips_3
            java.lang.String r2 = r9.getString(r2)
            kotlin.jvm.internal.Intrinsics.checkNotNullExpressionValue(r2, r7)
            r10.add(r2)
            java.util.Collections.shuffle(r10)
        L_0x008c:
            boolean r2 = r9.loading
            if (r2 == 0) goto L_0x00bc
            kotlinx.coroutines.h2 r2 = kotlinx.coroutines.z0.e()
            com.gpower.coloringbynumber.activity.ColoringActivity$previewLoadingStyleDefault$3 r7 = new com.gpower.coloringbynumber.activity.ColoringActivity$previewLoadingStyleDefault$3
            r7.<init>(r9, r10, r3)
            r0.L$0 = r10
            r0.label = r4
            java.lang.Object r2 = kotlinx.coroutines.h.h(r2, r7, r0)
            if (r2 != r1) goto L_0x00a4
            return r1
        L_0x00a4:
            r2 = r10
        L_0x00a5:
            int r10 = r9.countIndex
            int r10 = r10 + r6
            r9.countIndex = r10
            if (r10 != r5) goto L_0x00af
            r10 = 0
            r9.countIndex = r10
        L_0x00af:
            r0.L$0 = r2
            r0.label = r5
            r7 = 2000(0x7d0, double:9.88E-321)
            java.lang.Object r10 = kotlinx.coroutines.DelayKt.b(r7, r0)
            if (r10 != r1) goto L_0x0033
            return r1
        L_0x00bc:
            kotlin.Unit r10 = kotlin.Unit.f97091a
            return r10
        */
        throw new UnsupportedOperationException("Method not decompiled: com.gpower.coloringbynumber.activity.ColoringActivity.previewLoadingStyleDefault(kotlin.coroutines.Continuation):java.lang.Object");
    }

    private final void previewLoadingTvStyle() {
        y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(this), z0.c(), null, new ColoringActivity$previewLoadingTvStyle$1(this, null), 2, null);
    }

    private final void propGiftCloseAndRemoveHandler() {
        getMPopupPropsGift().dismiss();
        propGiftRemoveHandlerMessage();
    }

    private final Rect propGiftCreateViewRect(AppCompatImageView appCompatImageView) {
        int[] iArr = new int[2];
        appCompatImageView.getLocationOnScreen(iArr);
        int i10 = iArr[0];
        return new Rect(i10, iArr[1], appCompatImageView.getWidth() + i10, iArr[1] + appCompatImageView.getHeight());
    }

    private final void propGiftRemoveHandlerMessage() {
        this.mHandler.removeMessages(k2.h.f96944n);
    }

    /* access modifiers changed from: private */
    public final void propGiftRestartCalculationTime(int i10) {
        BeanTemplateInfoDBM beanTemplateInfo;
        com.base.module.tools.model_base.tools.p.a(o0.c.f99368h, "propGiftRestartCalculationTime type = " + i10);
        com.gpower.coloringbynumber.spf.c cVar = com.gpower.coloringbynumber.spf.c.f33083b;
        if (Intrinsics.g(cVar.q(), o0.a.f99347a.b())) {
            BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo = this.mRelationBean;
            if ((beanResourceRelationTemplateInfo == null || (beanTemplateInfo = beanResourceRelationTemplateInfo.getBeanTemplateInfo()) == null || !beanTemplateInfo.getShowClaimPropGift()) && !cVar.W0()) {
                propGiftRemoveHandlerMessage();
                this.mHandler.sendEmptyMessageDelayed(k2.h.f96944n, this.showPropGitDuration);
            }
        }
    }

    private final void propGiftShowPopupWindow() {
        ActivityEditBinding activityEditBinding;
        ImageView imageView;
        BeanTemplateInfoDBM beanTemplateInfo;
        BeanTemplateInfoDBM beanTemplateInfo2;
        if (!getMPopupPropsGift().isShowing() && !getMPopupClaimProp().isShowing() && !isFinishing() && !isDestroyed()) {
            BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo = this.mRelationBean;
            if ((beanResourceRelationTemplateInfo == null || (beanTemplateInfo2 = beanResourceRelationTemplateInfo.getBeanTemplateInfo()) == null || !beanTemplateInfo2.getShowClaimPropGift()) && !com.gpower.coloringbynumber.spf.c.f33083b.W0()) {
                BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo2 = this.mRelationBean;
                if ((beanResourceRelationTemplateInfo2 == null || (beanTemplateInfo = beanResourceRelationTemplateInfo2.getBeanTemplateInfo()) == null || beanTemplateInfo.isPainted() != 2) && (activityEditBinding = this.mBinding) != null && (imageView = activityEditBinding.idBack) != null) {
                    getMPopupPropsGift().h(imageView);
                    if (this.showPropGitDuration == 20000) {
                        this.showPropGitDuration = UnityAdsConstants.Timeout.INIT_TIMEOUT_MS;
                    }
                }
            }
        }
    }

    private final void propGiftStartClaimAnim(int i10) {
        ViewStubPaintToolsBinding viewStubPaintToolsBinding = this.mPaintToolsBinding;
        if (viewStubPaintToolsBinding != null) {
            AppCompatImageView appCompatImageView = viewStubPaintToolsBinding.ivHintIcon;
            Intrinsics.checkNotNullExpressionValue(appCompatImageView, "ivHintIcon");
            Rect propGiftCreateViewRect = propGiftCreateViewRect(appCompatImageView);
            AppCompatImageView appCompatImageView2 = viewStubPaintToolsBinding.ivAutoImg;
            Intrinsics.checkNotNullExpressionValue(appCompatImageView2, "ivAutoImg");
            Rect propGiftCreateViewRect2 = propGiftCreateViewRect(appCompatImageView2);
            com.base.module.tools.model_base.tools.p.a(o0.c.f99368h, "propGiftStartClaimAnim type = " + i10 + "  mHintRect = " + propGiftCreateViewRect + "  mAutoRect = " + propGiftCreateViewRect2);
            getMPopupClaimProp().g(propGiftCreateViewRect, propGiftCreateViewRect2, new Function0(i10, this) { // from class: com.gpower.coloringbynumber.activity.q0

                /* renamed from: n  reason: collision with root package name */
                public final /* synthetic */ int f32209n;

                /* renamed from: u  reason: collision with root package name */
                public final /* synthetic */ ColoringActivity f32210u;

                {
                    this.f32209n = r1;
                    this.f32210u = r2;
                }

                @Override // kotlin.jvm.functions.Function0
                public final Object invoke() {
                    return ColoringActivity.propGiftStartClaimAnim$lambda$139$lambda$138(this.f32209n, this.f32210u);
                }
            });
        }
    }

    /* access modifiers changed from: private */
    public static final Unit propGiftStartClaimAnim$lambda$139$lambda$138(int i10, ColoringActivity coloringActivity) {
        BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo;
        BeanTemplateInfoDBM beanTemplateInfo;
        if (!(i10 != 1 || (beanResourceRelationTemplateInfo = coloringActivity.mRelationBean) == null || (beanTemplateInfo = beanResourceRelationTemplateInfo.getBeanTemplateInfo()) == null)) {
            beanTemplateInfo.setShowClaimPropGift(true);
        }
        com.gpower.coloringbynumber.spf.c cVar = com.gpower.coloringbynumber.spf.c.f33083b;
        cVar.W1(cVar.n0() + 2);
        cVar.V1(cVar.m0() + 1);
        coloringActivity.setMAutoPaintFindNum(coloringActivity.getMAutoPaintFindNum() + 2);
        coloringActivity.setMColoringPropsFindNum(coloringActivity.getMColoringPropsFindNum() + 1);
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public final void pvBeginTemplateParse() {
        String str;
        TDEventUtils tDEventUtils = TDEventUtils.f33166a;
        BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo = this.mRelationBean;
        if (beanResourceRelationTemplateInfo == null || (str = beanResourceRelationTemplateInfo.itemCode()) == null) {
            str = "";
        }
        tDEventUtils.j(k2.d.P, k2.e.f96825j, str, k2.e.f96826k, k2.j.f96968d, "platform", k2.g.X0, "app_id", "9");
        this.pvBeginEventReport = true;
    }

    private final void pvEndExitTemplate() {
        String str;
        BeanTemplateInfoDBM beanTemplateInfo;
        String itemCode;
        String str2;
        BeanTemplateInfoDBM beanTemplateInfo2;
        String itemCode2;
        BeanTemplateInfoDBM beanTemplateInfo3;
        if (this.pvBeginEventReport) {
            BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo = this.mRelationBean;
            Integer num = null;
            if ((beanResourceRelationTemplateInfo == null || (beanTemplateInfo3 = beanResourceRelationTemplateInfo.getBeanTemplateInfo()) == null || beanTemplateInfo3.isPainted() != 2) && getMColorListAdapter().getSelectPosition() > -1) {
                num = Integer.valueOf(getMColorListAdapter().getMList().get(getMColorListAdapter().getSelectPosition()).curColorIndex());
            }
            int i10 = 0;
            if (num == null) {
                TDEventUtils tDEventUtils = TDEventUtils.f33166a;
                BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo2 = this.mRelationBean;
                if (beanResourceRelationTemplateInfo2 == null || (itemCode2 = beanResourceRelationTemplateInfo2.itemCode()) == null) {
                    str2 = "";
                } else {
                    str2 = itemCode2;
                }
                Integer valueOf = Integer.valueOf(this.colorNumber);
                Integer valueOf2 = Integer.valueOf(this.blockNumber);
                BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo3 = this.mRelationBean;
                if (!(beanResourceRelationTemplateInfo3 == null || (beanTemplateInfo2 = beanResourceRelationTemplateInfo3.getBeanTemplateInfo()) == null)) {
                    i10 = beanTemplateInfo2.getColorProgress();
                }
                tDEventUtils.k(k2.d.P, k2.e.f96825j, str2, k2.e.f96826k, k2.j.f96968d, "platform", k2.g.X0, "app_id", "9", k2.e.f96824i, valueOf, k2.e.f96823h, valueOf2, "progress", Integer.valueOf(i10));
                return;
            }
            TDEventUtils tDEventUtils2 = TDEventUtils.f33166a;
            BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo4 = this.mRelationBean;
            if (beanResourceRelationTemplateInfo4 == null || (itemCode = beanResourceRelationTemplateInfo4.itemCode()) == null) {
                str = "";
            } else {
                str = itemCode;
            }
            String valueOf3 = String.valueOf(num);
            Integer valueOf4 = Integer.valueOf(this.colorNumber);
            Integer valueOf5 = Integer.valueOf(this.blockNumber);
            BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo5 = this.mRelationBean;
            if (!(beanResourceRelationTemplateInfo5 == null || (beanTemplateInfo = beanResourceRelationTemplateInfo5.getBeanTemplateInfo()) == null)) {
                i10 = beanTemplateInfo.getColorProgress();
            }
            tDEventUtils2.k(k2.d.P, k2.e.f96825j, str, k2.e.f96826k, k2.j.f96968d, "platform", k2.g.X0, "color", valueOf3, "app_id", "9", k2.e.f96824i, valueOf4, k2.e.f96823h, valueOf5, "progress", Integer.valueOf(i10));
        }
    }

    private final void refreshAnim() {
        PlayerView playerView;
        ViewStubFinishViewBinding viewStubFinishViewBinding;
        PlayerView playerView2;
        if (!this.showAnimLoading) {
            ViewStubFinishViewBinding viewStubFinishViewBinding2 = this.mFinishResultBinding;
            if (!(viewStubFinishViewBinding2 == null || (playerView = viewStubFinishViewBinding2.editPathPlayerView) == null || playerView.getVisibility() != 0 || (viewStubFinishViewBinding = this.mFinishResultBinding) == null || (playerView2 = viewStubFinishViewBinding.editPathPlayerView) == null)) {
                playerView2.setVisibility(4);
            }
            this.showAnimLoading = true;
            dispatchStartPaintAnim();
        }
    }

    /* access modifiers changed from: private */
    public final void refreshRecyclerViewPosition(int i10) {
        RecyclerView recyclerView;
        RecyclerView.ViewHolder findViewHolderForLayoutPosition;
        ColorPaintedView colorPaintedView;
        ColorPaintedView colorPaintedView2;
        ColorPaintedView colorPaintedView3;
        ColorPaintedView colorPaintedView4;
        if (getMPopupSelectColor().isShowing()) {
            getMPopupSelectColor().dismiss();
        }
        if (i10 >= 0 && i10 < getMColorListAdapter().getMList().size()) {
            if (i10 != getMColorListAdapter().getSelectPosition()) {
                scrollRecyclerToPosition(i10);
            }
            ViewStubPaintToolsBinding viewStubPaintToolsBinding = this.mPaintToolsBinding;
            if (viewStubPaintToolsBinding != null && (recyclerView = viewStubPaintToolsBinding.svgColorListView) != null && (findViewHolderForLayoutPosition = recyclerView.findViewHolderForLayoutPosition(i10)) != null && (findViewHolderForLayoutPosition instanceof PaintColorListAdapter.MyViewHolder)) {
                int[] iArr = new int[2];
                ((PaintColorListAdapter.MyViewHolder) findViewHolderForLayoutPosition).getMColorCodeBallView().getLocationInWindow(iArr);
                ViewStubPaintToolsBinding viewStubPaintToolsBinding2 = this.mPaintToolsBinding;
                if (!(viewStubPaintToolsBinding2 == null || (colorPaintedView4 = viewStubPaintToolsBinding2.editSingleAnimView) == null)) {
                    colorPaintedView4.setTranslationX((float) iArr[0]);
                }
                ViewStubPaintToolsBinding viewStubPaintToolsBinding3 = this.mPaintToolsBinding;
                if (!(viewStubPaintToolsBinding3 == null || (colorPaintedView3 = viewStubPaintToolsBinding3.editSingleAnimView) == null)) {
                    colorPaintedView3.setTranslationY(0.0f);
                }
                ViewStubPaintToolsBinding viewStubPaintToolsBinding4 = this.mPaintToolsBinding;
                if (!(viewStubPaintToolsBinding4 == null || (colorPaintedView2 = viewStubPaintToolsBinding4.editSingleAnimView) == null)) {
                    colorPaintedView2.setScaleX(1.0f);
                }
                ViewStubPaintToolsBinding viewStubPaintToolsBinding5 = this.mPaintToolsBinding;
                if (viewStubPaintToolsBinding5 != null && (colorPaintedView = viewStubPaintToolsBinding5.editSingleAnimView) != null) {
                    colorPaintedView.setScaleY(1.0f);
                }
            }
        }
    }

    public static /* synthetic */ void refreshRecyclerViewPosition$default(ColoringActivity coloringActivity, int i10, int i11, Object obj) {
        if (obj == null) {
            if ((i11 & 1) != 0) {
                i10 = coloringActivity.getMColorListAdapter().getSelectPosition();
            }
            coloringActivity.refreshRecyclerViewPosition(i10);
            return;
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: refreshRecyclerViewPosition");
    }

    /* access modifiers changed from: private */
    public final void refreshSelectColorAnim(float f10) {
        RecyclerView.LayoutManager layoutManager;
        View view;
        RecyclerViewTopAnimView recyclerViewTopAnimView;
        RecyclerViewTopAnimView recyclerViewTopAnimView2;
        RecyclerView recyclerView;
        int selectPosition = getMColorListAdapter().getSelectPosition();
        if (selectPosition >= 0 && selectPosition < getMColorListAdapter().getMList().size()) {
            ViewStubPaintToolsBinding viewStubPaintToolsBinding = this.mPaintToolsBinding;
            Unit unit = null;
            if (viewStubPaintToolsBinding == null || (recyclerView = viewStubPaintToolsBinding.svgColorListView) == null) {
                layoutManager = null;
            } else {
                layoutManager = recyclerView.getLayoutManager();
            }
            if (layoutManager != null) {
                view = layoutManager.findViewByPosition(selectPosition);
            } else {
                view = null;
            }
            if (view != null) {
                int[] iArr = new int[2];
                view.getLocationOnScreen(iArr);
                ViewStubPaintToolsBinding viewStubPaintToolsBinding2 = this.mPaintToolsBinding;
                if (!(viewStubPaintToolsBinding2 == null || (recyclerViewTopAnimView2 = viewStubPaintToolsBinding2.editColoringAnimView) == null)) {
                    recyclerViewTopAnimView2.g(((float) iArr[0]) + (((float) view.getWidth()) / 2.0f), f10);
                    unit = Unit.f97091a;
                }
                if (unit != null) {
                    return;
                }
            }
            ViewStubPaintToolsBinding viewStubPaintToolsBinding3 = this.mPaintToolsBinding;
            if (viewStubPaintToolsBinding3 != null && (recyclerViewTopAnimView = viewStubPaintToolsBinding3.editColoringAnimView) != null) {
                recyclerViewTopAnimView.i();
                Unit unit2 = Unit.f97091a;
            }
        }
    }

    public static /* synthetic */ void refreshSelectColorAnim$default(ColoringActivity coloringActivity, float f10, int i10, Object obj) {
        if (obj == null) {
            if ((i10 & 1) != 0) {
                f10 = 1.0f;
            }
            coloringActivity.refreshSelectColorAnim(f10);
            return;
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: refreshSelectColorAnim");
    }

    /* access modifiers changed from: private */
    public final void refreshUnSelectColorAnim(float f10, BeanPaintColor beanPaintColor) {
        RecyclerView.LayoutManager layoutManager;
        View view;
        RecyclerViewTopAnimView recyclerViewTopAnimView;
        RecyclerViewTopAnimView recyclerViewTopAnimView2;
        RecyclerView recyclerView;
        int indexOf = getMColorListAdapter().getMList().indexOf(beanPaintColor);
        if (indexOf >= 0 && indexOf < getMColorListAdapter().getMList().size()) {
            ViewStubPaintToolsBinding viewStubPaintToolsBinding = this.mPaintToolsBinding;
            Unit unit = null;
            if (viewStubPaintToolsBinding == null || (recyclerView = viewStubPaintToolsBinding.svgColorListView) == null) {
                layoutManager = null;
            } else {
                layoutManager = recyclerView.getLayoutManager();
            }
            if (layoutManager != null) {
                view = layoutManager.findViewByPosition(indexOf);
            } else {
                view = null;
            }
            if (view != null) {
                int[] iArr = new int[2];
                view.getLocationOnScreen(iArr);
                ViewStubPaintToolsBinding viewStubPaintToolsBinding2 = this.mPaintToolsBinding;
                if (!(viewStubPaintToolsBinding2 == null || (recyclerViewTopAnimView2 = viewStubPaintToolsBinding2.editColoringAnimView) == null)) {
                    recyclerViewTopAnimView2.d(((float) iArr[0]) + (((float) view.getWidth()) / 2.0f), f10);
                    unit = Unit.f97091a;
                }
                if (unit != null) {
                    return;
                }
            }
            ViewStubPaintToolsBinding viewStubPaintToolsBinding3 = this.mPaintToolsBinding;
            if (viewStubPaintToolsBinding3 != null && (recyclerViewTopAnimView = viewStubPaintToolsBinding3.editColoringAnimView) != null) {
                recyclerViewTopAnimView.f();
                Unit unit2 = Unit.f97091a;
            }
        }
    }

    private final void requestPermissionFinish() {
        Runnable runnable = this.mSaveRunnable;
        if (runnable != null) {
            runnable.run();
        }
        this.mSaveRunnable = null;
        Runnable runnable2 = this.mShareRunnable;
        if (runnable2 != null) {
            runnable2.run();
        }
        this.mShareRunnable = null;
    }

    private final void resultViewBgAnim() {
        ViewStubResultBgBinding viewStubResultBgBinding;
        ConstraintLayout constraintLayout;
        ViewStub viewStub;
        View inflate;
        ActivityEditBinding activityEditBinding = this.mBinding;
        ConstraintLayout constraintLayout2 = null;
        if (activityEditBinding == null || (viewStub = activityEditBinding.editPathIncludeBg) == null || (inflate = viewStub.inflate()) == null) {
            viewStubResultBgBinding = null;
        } else {
            viewStubResultBgBinding = ViewStubResultBgBinding.bind(inflate);
        }
        this.mResultBgBinding = viewStubResultBgBinding;
        if (!(viewStubResultBgBinding == null || (constraintLayout = viewStubResultBgBinding.editPathIncludeRootLayout) == null)) {
            constraintLayout.setVisibility(0);
        }
        ViewStubResultBgBinding viewStubResultBgBinding2 = this.mResultBgBinding;
        if (viewStubResultBgBinding2 != null) {
            constraintLayout2 = viewStubResultBgBinding2.editPathIncludeRootLayout;
        }
        ObjectAnimator ofFloat = ObjectAnimator.ofFloat(constraintLayout2, "alpha", 0.0f, 1.0f);
        ofFloat.setInterpolator(new AccelerateDecelerateInterpolator());
        ofFloat.setDuration(700L);
        ofFloat.start();
    }

    /* access modifiers changed from: private */
    public final Object saveTemplatePic(Function1<? super Continuation<? super Unit>, ? extends Object> function1, Continuation<? super Unit> continuation) {
        this.fileNameStrings.clear();
        this.fileMimeString.clear();
        Object dispatchSaveTemplatePic = dispatchSaveTemplatePic(new ColoringActivity$saveTemplatePic$2(this, function1, null), continuation);
        if (dispatchSaveTemplatePic == kotlin.coroutines.intrinsics.b.l()) {
            return dispatchSaveTemplatePic;
        }
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public final void saveVideo(boolean z10) {
        if (!this.mIsGenerateVideo) {
            this.isShareVideo = z10;
            this.mIsGenerateVideo = true;
            dispatchDownloadVideo();
        }
    }

    public static /* synthetic */ void saveVideo$default(ColoringActivity coloringActivity, boolean z10, int i10, Object obj) {
        if (obj == null) {
            if ((i10 & 1) != 0) {
                z10 = false;
            }
            coloringActivity.saveVideo(z10);
            return;
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: saveVideo");
    }

    /* access modifiers changed from: private */
    public final void scanFileToAlbum() {
        if (this.mSingleMediaScanner == null) {
            this.mSingleMediaScanner = new g0(this, this.fileNameStrings, this.fileMimeString);
        }
        g0 g0Var = this.mSingleMediaScanner;
        if (g0Var != null) {
            g0Var.a();
        }
    }

    private final void scrollRecyclerToPosition(int i10) {
        RecyclerView recyclerView;
        int i11;
        ViewStubPaintToolsBinding viewStubPaintToolsBinding = this.mPaintToolsBinding;
        if (viewStubPaintToolsBinding != null && (recyclerView = viewStubPaintToolsBinding.svgColorListView) != null) {
            RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();
            if (layoutManager instanceof LinearLayoutManager) {
                RecyclerView.ViewHolder findViewHolderForLayoutPosition = recyclerView.findViewHolderForLayoutPosition(i10);
                h0.c cVar = h0.c.f88264a;
                int a10 = (int) cVar.a();
                int d10 = (int) (((float) cVar.d()) * 0.65f);
                if (i10 > getMColorListAdapter().getSelectPosition()) {
                    i11 = d10;
                } else {
                    i11 = a10;
                }
                if (findViewHolderForLayoutPosition instanceof PaintColorListAdapter.MyViewHolder) {
                    int[] iArr = new int[2];
                    ((PaintColorListAdapter.MyViewHolder) findViewHolderForLayoutPosition).getMColorCodeBallView().getLocationInWindow(iArr);
                    int i12 = iArr[0];
                    if (a10 > i12 || i12 > d10) {
                        ((LinearLayoutManager) layoutManager).scrollToPositionWithOffset(i10, i11);
                    } else {
                        ((LinearLayoutManager) layoutManager).scrollToPosition(i10);
                    }
                } else {
                    ((LinearLayoutManager) layoutManager).scrollToPositionWithOffset(i10, i11);
                }
            }
        }
    }

    private final void setMAutoPaintFindNum(int i10) {
        this.mAutoPaintFindNum$delegate.setValue(this, $$delegatedProperties[0], Integer.valueOf(i10));
    }

    private final void setMColoringPropsFindNum(int i10) {
        this.mColoringPropsFindNum$delegate.setValue(this, $$delegatedProperties[1], Integer.valueOf(i10));
    }

    /* access modifiers changed from: private */
    public final void shareFile(String str, String str2) {
        if (this.shareToAction == null) {
            this.shareToAction = new com.gpower.coloringbynumber.tools.e0();
        }
        com.gpower.coloringbynumber.tools.e0 e0Var = this.shareToAction;
        if (e0Var != null) {
            e0Var.b(this, getString(R.string.tell_friend_text), getString(R.string.tell_friend_text), str, str2);
        }
    }

    private final void showAlphaView(View view, Function0<Unit> function0) {
        ObjectAnimator ofFloat = ObjectAnimator.ofFloat(view, "alpha", 0.0f, 1.0f);
        ofFloat.setDuration(300L);
        Intrinsics.m(ofFloat);
        ofFloat.addListener(new l(function0));
        ofFloat.start();
    }

    /* access modifiers changed from: private */
    public final void showBottomToolsLayoutAnim() {
        ConstraintLayout constraintLayout;
        ConstraintLayout constraintLayout2;
        ViewStubPaintToolsBinding viewStubPaintToolsBinding = this.mPaintToolsBinding;
        if (!(viewStubPaintToolsBinding == null || (constraintLayout2 = viewStubPaintToolsBinding.activityEditPathBottomLayout) == null)) {
            constraintLayout2.setVisibility(0);
        }
        Animation loadAnimation = AnimationUtils.loadAnimation(this, R.anim.tools_trans_show);
        ViewStubPaintToolsBinding viewStubPaintToolsBinding2 = this.mPaintToolsBinding;
        if (viewStubPaintToolsBinding2 != null && (constraintLayout = viewStubPaintToolsBinding2.activityEditPathBottomLayout) != null) {
            constraintLayout.startAnimation(loadAnimation);
        }
    }

    private final void showDownloadPop(String str, Function1<? super String, Unit> function1, Function1<? super String, Unit> function12) {
        if (this.mPopupDownload == null) {
            this.mPopupDownload = new com.gpower.coloringbynumber.pop.s(this, null, 2, null);
        }
        com.gpower.coloringbynumber.pop.s sVar = this.mPopupDownload;
        if (sVar != null) {
            sVar.r(str);
        }
        y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(this), z0.c(), null, new ColoringActivity$showDownloadPop$1(this, function1, function12, null), 2, null);
    }

    /* access modifiers changed from: private */
    public final void showFinishFlag() {
        Object obj;
        int i10;
        float f10;
        ViewStubFinishViewBinding viewStubFinishViewBinding;
        AppCompatImageView appCompatImageView;
        BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo;
        AppCompatImageView appCompatImageView2;
        RecyclerViewTopAnimView recyclerViewTopAnimView;
        String str;
        ViewStub viewStub;
        View inflate;
        ConstraintLayout constraintLayout;
        ConstraintLayout constraintLayout2;
        try {
            Result.a aVar = Result.Companion;
        } catch (Throwable th) {
            Result.a aVar2 = Result.Companion;
            obj = Result.m4962constructorimpl(s0.a(th));
        }
        if (!this.isFinished) {
            String i11 = com.gpower.coloringbynumber.tools.l.i(System.currentTimeMillis());
            com.gpower.coloringbynumber.spf.c cVar = com.gpower.coloringbynumber.spf.c.f33083b;
            if (!Intrinsics.g(cVar.u0(), i11)) {
                cVar.d2(i11);
                cVar.t1(0);
            }
            cVar.t1(cVar.K() + 1);
            ConstraintLayout constraintLayout3 = null;
            y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(this), z0.c(), null, new ColoringActivity$showFinishFlag$1$1(null), 2, null);
            ViewStubPaintToolsBinding viewStubPaintToolsBinding = this.mPaintToolsBinding;
            if (!(viewStubPaintToolsBinding == null || (constraintLayout2 = viewStubPaintToolsBinding.viewStubHintRootLayout) == null)) {
                q0.a(constraintLayout2, false);
            }
            this.isFinished = true;
            this.curPaintStage = 3;
            ActivityEditBinding activityEditBinding = this.mBinding;
            if (activityEditBinding == null || (constraintLayout = activityEditBinding.editTotalLayout) == null) {
                i10 = h0.c.f88264a.c();
            } else {
                i10 = constraintLayout.getHeight();
            }
            float f11 = (((float) i10) / 3.0f) * 2.0f;
            h0.c cVar2 = h0.c.f88264a;
            if (cVar2.b()) {
                f10 = ((float) cVar2.d()) * 0.66f;
            } else {
                f10 = ((float) cVar2.d()) - com.base.module.tools.model_base.tools.m.a(44.0f);
            }
            float f12 = 0.74013156f * f11;
            float f13 = f11 * 0.21710527f;
            this.mFinishTopPadding = f13;
            float f14 = f10 / this.entityWidth;
            float min = Math.min(f14, f12 / this.entityHeight);
            int i12 = (min > f14 ? 1 : (min == f14 ? 0 : -1));
            if (i12 != 0) {
                f10 = this.entityWidth * min;
            }
            if (i12 == 0) {
                f12 = this.entityHeight * min;
            }
            float d10 = (((float) cVar2.d()) - f10) / 2.0f;
            this.hobbyMarginTop = (int) (f12 + f13 + com.base.module.tools.model_base.tools.m.a(10.0f));
            ActivityEditBinding activityEditBinding2 = this.mBinding;
            if (activityEditBinding2 == null || (viewStub = activityEditBinding2.viewStubPaintFinishLayout) == null || (inflate = viewStub.inflate()) == null) {
                viewStubFinishViewBinding = null;
            } else {
                viewStubFinishViewBinding = ViewStubFinishViewBinding.bind(inflate);
            }
            this.mFinishResultBinding = viewStubFinishViewBinding;
            if (viewStubFinishViewBinding != null) {
                viewStubFinishViewBinding.editPathWaterMaskIcon.setOnClickListener(this);
                LottieAnimationView lottieAnimationView = viewStubFinishViewBinding.lottieLike;
                if (!this.isLike) {
                    str = "result_like.json";
                } else {
                    str = "result_like_2.json";
                }
                lottieAnimationView.setAnimation(str);
                viewStubFinishViewBinding.lottieLike.addAnimatorListener(new m(viewStubFinishViewBinding, this));
                viewStubFinishViewBinding.editPathFinishSkipIcon.setOnClickListener(this);
                viewStubFinishViewBinding.editPathFinishContinueTv.setOnClickListener(this);
                viewStubFinishViewBinding.editPathFinishShareIcon.setOnClickListener(this);
                viewStubFinishViewBinding.editPathFinishSaveIcon.setOnClickListener(this);
                viewStubFinishViewBinding.editPathFinishLikeIcon.setOnClickListener(this);
                TextView textView = viewStubFinishViewBinding.skipContentTv;
                Intrinsics.checkNotNullExpressionValue(textView, "skipContentTv");
                ViewGroup.LayoutParams layoutParams = textView.getLayoutParams();
                if (layoutParams != null) {
                    ConstraintLayout.LayoutParams layoutParams2 = (ConstraintLayout.LayoutParams) layoutParams;
                    ((ViewGroup.MarginLayoutParams) layoutParams2).topMargin = this.hobbyMarginTop;
                    textView.setLayoutParams(layoutParams2);
                    String valueOf = String.valueOf(cVar.K());
                    String string = getString(R.string.amazing_info, valueOf);
                    Intrinsics.checkNotNullExpressionValue(string, "getString(...)");
                    SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(string);
                    int J3 = kotlin.text.g0.J3(string, valueOf, 0, false, 6, null);
                    spannableStringBuilder.setSpan(new ForegroundColorSpan(Color.parseColor("#9A62FF")), J3, valueOf.length() + J3, 33);
                    viewStubFinishViewBinding.skipContentTv.setText(spannableStringBuilder);
                } else {
                    throw new NullPointerException("null cannot be cast to non-null type androidx.constraintlayout.widget.ConstraintLayout.LayoutParams");
                }
            }
            dispatchPaintedFinish(d10, this.mFinishTopPadding);
            propGiftCloseAndRemoveHandler();
            dispatchSaveDrawInfoToDB(1);
            ViewStubPaintToolsBinding viewStubPaintToolsBinding2 = this.mPaintToolsBinding;
            if (viewStubPaintToolsBinding2 != null) {
                constraintLayout3 = viewStubPaintToolsBinding2.activityEditPathBottomLayout;
            }
            showGoneAnimation$default(this, constraintLayout3, 0, 2, null);
            ViewStubPaintToolsBinding viewStubPaintToolsBinding3 = this.mPaintToolsBinding;
            if (!(viewStubPaintToolsBinding3 == null || (recyclerViewTopAnimView = viewStubPaintToolsBinding3.editColoringAnimView) == null)) {
                recyclerViewTopAnimView.setVisibility(8);
            }
            ViewStubPaintToolsBinding viewStubPaintToolsBinding4 = this.mPaintToolsBinding;
            if (!(viewStubPaintToolsBinding4 == null || (appCompatImageView2 = viewStubPaintToolsBinding4.btnScale) == null)) {
                appCompatImageView2.setVisibility(8);
            }
            cVar.C1(cVar.T() + 1);
            int T = cVar.T();
            if (T == 1) {
                TDEventUtils.f33166a.p(k2.g.V);
            } else if (T == 2) {
                TDEventUtils.f33166a.p(k2.g.W);
            } else if (T == 3) {
                TDEventUtils.f33166a.p(k2.g.X);
            } else if (T == 5) {
                TDEventUtils.f33166a.p(k2.g.Y);
            } else if (T == 7) {
                TDEventUtils.f33166a.p(k2.g.Z);
            } else if (T == 10) {
                TDEventUtils.f33166a.p(k2.g.f96854a0);
            }
            App.a aVar3 = App.I;
            ViewModelAchievement i13 = aVar3.b().i();
            if (i13 != null) {
                i13.achievementTypeAddProgress(1);
            }
            ViewModelAchievement i14 = aVar3.b().i();
            if (i14 != null) {
                i14.achievementTypeCurDayFinishNumber();
            }
            categoryAchievement();
            eventReportUserProgress();
            eventReportFinishPic();
            insertHobbyCollection();
            bindExoPlayer();
            if (Intrinsics.g(this.abTestResultCollection, o0.a.f99347a.b()) && (beanResourceRelationTemplateInfo = this.mRelationBean) != null) {
                getMColorViewModel().queryTemplateCategory(beanResourceRelationTemplateInfo);
            }
            ViewStubPaintToolsBinding viewStubPaintToolsBinding5 = this.mPaintToolsBinding;
            if (!(viewStubPaintToolsBinding5 == null || (appCompatImageView = viewStubPaintToolsBinding5.btnScale) == null)) {
                appCompatImageView.setVisibility(8);
            }
            Message obtain = Message.obtain();
            obtain.what = k2.h.f96937g;
            obtain.obj = 0;
            this.mHandler.sendMessageDelayed(obtain, 50);
            hideBannerAds();
            AdsActivityManager.f33023a.o(k2.f.f96843b);
            obj = Result.m4962constructorimpl(Unit.f97091a);
            Throwable r02 = Result.m4965exceptionOrNullimpl(obj);
            if (r02 != null) {
                com.base.module.tools.model_base.tools.p.a(o0.c.f99366f, "ColoringActivity showFinishFlag onFailure = " + r02.getMessage());
            }
        }
    }

    private final void showGoneAnimation(View view, long j10) {
        if (view != null) {
            if (j10 == 0) {
                view.setVisibility(8);
                return;
            }
            TranslateAnimation translateAnimation = new TranslateAnimation(1, 0.0f, 1, 0.0f, 1, 0.0f, 1, 1.0f);
            translateAnimation.setDuration(j10);
            view.startAnimation(translateAnimation);
            view.setVisibility(8);
        }
    }

    public static /* synthetic */ void showGoneAnimation$default(ColoringActivity coloringActivity, View view, long j10, int i10, Object obj) {
        if (obj == null) {
            if ((i10 & 2) != 0) {
                j10 = 1000;
            }
            coloringActivity.showGoneAnimation(view, j10);
            return;
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: showGoneAnimation");
    }

    private final void showHobbyView(V25BeanCategoryDBM v25BeanCategoryDBM) {
        TextView textView;
        ViewStubHobbyTipBinding viewStubHobbyTipBinding;
        ViewStub viewStub;
        View inflate;
        if (this.mHobbyIconBinding == null) {
            ViewStubFinishViewBinding viewStubFinishViewBinding = this.mFinishResultBinding;
            if (viewStubFinishViewBinding == null || (viewStub = viewStubFinishViewBinding.hobbyViewStub) == null || (inflate = viewStub.inflate()) == null) {
                viewStubHobbyTipBinding = null;
            } else {
                viewStubHobbyTipBinding = ViewStubHobbyTipBinding.bind(inflate);
            }
            this.mHobbyIconBinding = viewStubHobbyTipBinding;
        }
        ViewStubHobbyTipBinding viewStubHobbyTipBinding2 = this.mHobbyIconBinding;
        if (viewStubHobbyTipBinding2 != null) {
            View view = viewStubHobbyTipBinding2.hobbyTipBg;
            Intrinsics.checkNotNullExpressionValue(view, "hobbyTipBg");
            ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
            if (layoutParams != null) {
                ConstraintLayout.LayoutParams layoutParams2 = (ConstraintLayout.LayoutParams) layoutParams;
                ((ViewGroup.MarginLayoutParams) layoutParams2).topMargin = this.hobbyMarginTop;
                view.setLayoutParams(layoutParams2);
                AppCompatTextView appCompatTextView = viewStubHobbyTipBinding2.hobbyTipTv;
                String showTitle = v25BeanCategoryDBM.getShowTitle();
                if (showTitle == null && (showTitle = v25BeanCategoryDBM.getDefaultText()) == null) {
                    showTitle = "";
                }
                appCompatTextView.setText(showTitle);
                String worksTotal = v25BeanCategoryDBM.getWorksTotal();
                viewStubHobbyTipBinding2.hobbyNumberTv.setText("(" + (v25BeanCategoryDBM.getPaintNumber() + 1) + RemoteSettings.FORWARD_SLASH_STRING + worksTotal + ")");
                viewStubHobbyTipBinding2.hobbyIconView.startHobby(Intrinsics.g(v25BeanCategoryDBM.getSubType(), k2.j.f96986v) ^ true);
                String icon = v25BeanCategoryDBM.getIcon();
                if (icon != null) {
                    Glide.with((FragmentActivity) this).load(icon).override(com.base.module.tools.model_base.tools.m.b(40.0f)).into(viewStubHobbyTipBinding2.hobbyIconView);
                }
                viewStubHobbyTipBinding2.hobbyTipBg.setOnClickListener(new View.OnClickListener(this) { // from class: com.gpower.coloringbynumber.activity.s

                    /* renamed from: u  reason: collision with root package name */
                    public final /* synthetic */ ColoringActivity f32223u;

                    {
                        this.f32223u = r2;
                    }

                    @Override // android.view.View.OnClickListener
                    public final void onClick(View view2) {
                        ColoringActivity.showHobbyView$lambda$60$lambda$59(V25BeanCategoryDBM.this, this.f32223u, view2);
                    }
                });
                ViewStubFinishViewBinding viewStubFinishViewBinding2 = this.mFinishResultBinding;
                if (viewStubFinishViewBinding2 != null && (textView = viewStubFinishViewBinding2.skipContentTv) != null) {
                    textView.setVisibility(8);
                    return;
                }
                return;
            }
            throw new NullPointerException("null cannot be cast to non-null type androidx.constraintlayout.widget.ConstraintLayout.LayoutParams");
        }
    }

    /* access modifiers changed from: private */
    public static final void showHobbyView$lambda$60$lambda$59(V25BeanCategoryDBM v25BeanCategoryDBM, ColoringActivity coloringActivity, View view) {
        if (Intrinsics.g(v25BeanCategoryDBM.getSubType(), k2.j.f96986v)) {
            ExploreThemeContentActivity.a.b(ExploreThemeContentActivity.Companion, coloringActivity, v25BeanCategoryDBM.getId(), k2.d.f96814y, v25BeanCategoryDBM.getDefaultText(), null, 16, null);
            coloringActivity.exitActivity();
            return;
        }
        HobbyCollectionActivity.a aVar = HobbyCollectionActivity.Companion;
        String id2 = v25BeanCategoryDBM.getId();
        String defaultText = v25BeanCategoryDBM.getDefaultText();
        if (defaultText == null) {
            defaultText = "";
        }
        HobbyCollectionActivity.a.b(aVar, coloringActivity, id2, defaultText, null, k2.g.f96865e, 8, null);
        coloringActivity.exitActivity();
    }

    /* access modifiers changed from: private */
    public final void showInterstitialAdvAndTD(Function1<? super Boolean, Unit> function1) {
        if (h0.c.f88264a.g()) {
            y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(this), z0.c(), null, new ColoringActivity$showInterstitialAdvAndTD$1(this, function1, null), 2, null);
        } else if (function1 != null) {
            function1.invoke(Boolean.FALSE);
        }
    }

    /* JADX DEBUG: Multi-variable search result rejected for r0v0, resolved type: com.gpower.coloringbynumber.activity.ColoringActivity */
    /* JADX WARN: Multi-variable type inference failed */
    public static /* synthetic */ void showInterstitialAdvAndTD$default(ColoringActivity coloringActivity, Function1 function1, int i10, Object obj) {
        if (obj == null) {
            if ((i10 & 1) != 0) {
                function1 = null;
            }
            coloringActivity.showInterstitialAdvAndTD(function1);
            return;
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: showInterstitialAdvAndTD");
    }

    private final void showPropsAddAnim(int i10, RewardCategory rewardCategory) {
        float f10;
        ViewStubPropsHintStartBinding viewStubPropsHintStartBinding;
        ViewStub viewStub;
        View inflate;
        if (this.mViewPropHintBinding == null) {
            ActivityEditBinding activityEditBinding = this.mBinding;
            if (activityEditBinding == null || (viewStub = activityEditBinding.viewPropsHintStart) == null || (inflate = viewStub.inflate()) == null) {
                viewStubPropsHintStartBinding = null;
            } else {
                viewStubPropsHintStartBinding = ViewStubPropsHintStartBinding.bind(inflate);
            }
            this.mViewPropHintBinding = viewStubPropsHintStartBinding;
        }
        ViewStubPropsHintStartBinding viewStubPropsHintStartBinding2 = this.mViewPropHintBinding;
        if (viewStubPropsHintStartBinding2 != null) {
            if (this.mPropsFindPair == null) {
                this.mPropsFindPair = new Pair<>(Float.valueOf(viewStubPropsHintStartBinding2.clPropsHintStart.getX()), Float.valueOf(viewStubPropsHintStartBinding2.clPropsHintStart.getY()));
            } else {
                viewStubPropsHintStartBinding2.clPropsHintStart.setAlpha(1.0f);
                ConstraintLayout constraintLayout = viewStubPropsHintStartBinding2.clPropsHintStart;
                Pair<Float, Float> pair = this.mPropsFindPair;
                float f11 = 0.0f;
                if (pair != null) {
                    f10 = pair.getFirst().floatValue();
                } else {
                    f10 = 0.0f;
                }
                constraintLayout.setTranslationX(f10);
                ConstraintLayout constraintLayout2 = viewStubPropsHintStartBinding2.clPropsHintStart;
                Pair<Float, Float> pair2 = this.mPropsFindPair;
                if (pair2 != null) {
                    f11 = pair2.getSecond().floatValue();
                }
                constraintLayout2.setTranslationY(f11);
                viewStubPropsHintStartBinding2.clPropsHintStart.setScaleX(1.0f);
                viewStubPropsHintStartBinding2.clPropsHintStart.setScaleY(1.0f);
            }
            viewStubPropsHintStartBinding2.clPropsHintStart.setVisibility(0);
            viewStubPropsHintStartBinding2.tvPropsHintStart.setText(getString(R.string.add_count, Integer.valueOf(i10)));
            if (rewardCategory == RewardCategory.EDIT_COLOR_AUTO_TOP) {
                viewStubPropsHintStartBinding2.clHintIconImg.setImageResource(R.drawable.ic_auto_anim_icon);
            } else {
                viewStubPropsHintStartBinding2.clHintIconImg.setImageResource(R.drawable.ic_hint_anim_icon);
            }
            AnimUtilsKt.L(viewStubPropsHintStartBinding2.clPropsHintStart, new Function0(rewardCategory, this, i10) { // from class: com.gpower.coloringbynumber.activity.f1

                /* renamed from: u  reason: collision with root package name */
                public final /* synthetic */ RewardCategory f32142u;

                /* renamed from: v  reason: collision with root package name */
                public final /* synthetic */ ColoringActivity f32143v;

                /* renamed from: w  reason: collision with root package name */
                public final /* synthetic */ int f32144w;

                {
                    this.f32142u = r2;
                    this.f32143v = r3;
                    this.f32144w = r4;
                }

                @Override // kotlin.jvm.functions.Function0
                public final Object invoke() {
                    return ColoringActivity.showPropsAddAnim$lambda$125$lambda$124(ViewStubPropsHintStartBinding.this, this.f32142u, this.f32143v, this.f32144w);
                }
            });
        }
    }

    /* access modifiers changed from: private */
    public static final Unit showPropsAddAnim$lambda$125$lambda$124(ViewStubPropsHintStartBinding viewStubPropsHintStartBinding, RewardCategory rewardCategory, ColoringActivity coloringActivity, int i10) {
        AppCompatImageView appCompatImageView;
        ConstraintLayout constraintLayout = viewStubPropsHintStartBinding.clPropsHintStart;
        if (rewardCategory == RewardCategory.EDIT_COLOR_AUTO_TOP) {
            ViewStubPaintToolsBinding viewStubPaintToolsBinding = coloringActivity.mPaintToolsBinding;
            if (viewStubPaintToolsBinding != null) {
                appCompatImageView = viewStubPaintToolsBinding.ivAutoImg;
            }
            appCompatImageView = null;
        } else {
            ViewStubPaintToolsBinding viewStubPaintToolsBinding2 = coloringActivity.mPaintToolsBinding;
            if (viewStubPaintToolsBinding2 != null) {
                appCompatImageView = viewStubPaintToolsBinding2.ivHintIcon;
            }
            appCompatImageView = null;
        }
        AnimUtilsKt.G(constraintLayout, appCompatImageView, 0, new Function0(rewardCategory, coloringActivity, i10) { // from class: com.gpower.coloringbynumber.activity.z1

            /* renamed from: u  reason: collision with root package name */
            public final /* synthetic */ RewardCategory f32268u;

            /* renamed from: v  reason: collision with root package name */
            public final /* synthetic */ ColoringActivity f32269v;

            /* renamed from: w  reason: collision with root package name */
            public final /* synthetic */ int f32270w;

            {
                this.f32268u = r2;
                this.f32269v = r3;
                this.f32270w = r4;
            }

            @Override // kotlin.jvm.functions.Function0
            public final Object invoke() {
                return ColoringActivity.showPropsAddAnim$lambda$125$lambda$124$lambda$123(ViewStubPropsHintStartBinding.this, this.f32268u, this.f32269v, this.f32270w);
            }
        }, 4, null);
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit showPropsAddAnim$lambda$125$lambda$124$lambda$123(ViewStubPropsHintStartBinding viewStubPropsHintStartBinding, RewardCategory rewardCategory, ColoringActivity coloringActivity, int i10) {
        viewStubPropsHintStartBinding.clPropsHintStart.setVisibility(4);
        if (rewardCategory == RewardCategory.EDIT_COLOR_AUTO_TOP) {
            coloringActivity.setMAutoPaintFindNum(coloringActivity.getMAutoPaintFindNum() + i10);
            if (coloringActivity.getMAutoPaintFindNum() > 0) {
                coloringActivity.setMAutoPaintFindNum(coloringActivity.getMAutoPaintFindNum() - 1);
                coloringActivity.dispatchAutoPaint(coloringActivity.autoPaintTime);
            }
        } else {
            coloringActivity.setMColoringPropsFindNum(coloringActivity.getMColoringPropsFindNum() + i10);
        }
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public final void showRemoveWaterMarkActivity() {
        if (this.isBoughtCollectPackage || com.gpower.coloringbynumber.spf.c.f33083b.W0()) {
            dispatchSubStatusChange(true);
            return;
        }
        this.mRewardCategory = RewardCategory.WATER_MARK;
        showRewardVideo$default(this, "watermark", false, 2, null);
    }

    /* access modifiers changed from: private */
    public final void showResultAnim(RectF rectF) {
        LottieAnimationView lottieAnimationView;
        LottieAnimationView lottieAnimationView2;
        LottieAnimationView lottieAnimationView3;
        ViewGroup.LayoutParams layoutParams;
        PlayerView playerView;
        PlayerView playerView2;
        ViewStubFinishViewBinding viewStubFinishViewBinding = this.mFinishResultBinding;
        if (!(viewStubFinishViewBinding == null || (playerView2 = viewStubFinishViewBinding.editPathPlayerView) == null)) {
            playerView2.setOutlineProvider(new n());
        }
        ViewStubFinishViewBinding viewStubFinishViewBinding2 = this.mFinishResultBinding;
        if (!(viewStubFinishViewBinding2 == null || (playerView = viewStubFinishViewBinding2.editPathPlayerView) == null)) {
            playerView.setClipToOutline(true);
        }
        ViewStubFinishViewBinding viewStubFinishViewBinding3 = this.mFinishResultBinding;
        if (!(viewStubFinishViewBinding3 == null || (lottieAnimationView3 = viewStubFinishViewBinding3.paintFinishAnimation) == null || (layoutParams = lottieAnimationView3.getLayoutParams()) == null)) {
            layoutParams.height = (int) ((((float) h0.c.f88264a.c()) / 3.0f) * 2.0f);
        }
        ViewStubFinishViewBinding viewStubFinishViewBinding4 = this.mFinishResultBinding;
        if (!(viewStubFinishViewBinding4 == null || (lottieAnimationView2 = viewStubFinishViewBinding4.paintFinishAnimation) == null)) {
            lottieAnimationView2.setVisibility(0);
        }
        ViewStubFinishViewBinding viewStubFinishViewBinding5 = this.mFinishResultBinding;
        if (!(viewStubFinishViewBinding5 == null || (lottieAnimationView = viewStubFinishViewBinding5.paintFinishAnimation) == null)) {
            lottieAnimationView.playAnimation();
        }
        resultViewBgAnim();
        y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(this), z0.c(), null, new ColoringActivity$showResultAnim$2(this, rectF, null), 2, null);
        y1 unused2 = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(this), z0.c(), null, new ColoringActivity$showResultAnim$3(rectF, this, null), 2, null);
    }

    private final void showRewardAdvLoadingDialog(FragmentManager fragmentManager, String str) {
        AdvLoadingDialogFragment b10 = AdvLoadingDialogFragment.a.b(AdvLoadingDialogFragment.Companion, null, str, 1, null);
        b10.setOnAdvLoadingListener(new Function3() { // from class: com.gpower.coloringbynumber.activity.t0
            @Override // kotlin.jvm.functions.Function3
            public final Object invoke(Object obj, Object obj2, Object obj3) {
                return ColoringActivity.showRewardAdvLoadingDialog$lambda$131$lambda$130(ColoringActivity.this, ((Boolean) obj).booleanValue(), ((Boolean) obj2).booleanValue(), (String) obj3);
            }
        });
        b10.show(fragmentManager, "AdvLoadingDialogFragment");
    }

    /* access modifiers changed from: private */
    public static final Unit showRewardAdvLoadingDialog$lambda$131$lambda$130(ColoringActivity coloringActivity, boolean z10, boolean z11, String str) {
        Intrinsics.checkNotNullParameter(str, "scenes");
        if (z10) {
            coloringActivity.showRewardVideo(str, true);
        } else if (Intrinsics.g(str, k2.g.f96925y) || Intrinsics.g(str, "watermark")) {
            TDEventUtils.l(str);
        }
        return Unit.f97091a;
    }

    public static /* synthetic */ void showRewardVideo$default(ColoringActivity coloringActivity, String str, boolean z10, int i10, Object obj) {
        if (obj == null) {
            if ((i10 & 2) != 0) {
                z10 = false;
            }
            coloringActivity.showRewardVideo(str, z10);
            return;
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: showRewardVideo");
    }

    /* access modifiers changed from: private */
    public static final Unit showRewardVideo$lambda$126(boolean z10, ColoringActivity coloringActivity, int i10) {
        if (z10 && i10 != 1 && coloringActivity.mRewardCategory == RewardCategory.EDIT_COLOR_PROP_GIFT) {
            coloringActivity.propGiftRestartCalculationTime(6);
        }
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit showRewardVideo$lambda$127(ColoringActivity coloringActivity, String str, int i10) {
        if (i10 != 1) {
            FragmentManager supportFragmentManager = coloringActivity.getSupportFragmentManager();
            Intrinsics.checkNotNullExpressionValue(supportFragmentManager, "getSupportFragmentManager(...)");
            coloringActivity.showRewardAdvLoadingDialog(supportFragmentManager, str);
        }
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit showRewardVideo$lambda$129(ColoringActivity coloringActivity, boolean z10) {
        BeanTemplateInfoDBM beanTemplateInfo;
        ImageView imageView;
        ImageView imageView2;
        if (z10) {
            switch (a.$EnumSwitchMapping$0[coloringActivity.mRewardCategory.ordinal()]) {
                case 1:
                    com.gpower.coloringbynumber.spf.c cVar = com.gpower.coloringbynumber.spf.c.f33083b;
                    cVar.W1(cVar.n0() + 1);
                    coloringActivity.showPropsAddAnim(1, coloringActivity.mRewardCategory);
                    break;
                case 2:
                    com.gpower.coloringbynumber.spf.c cVar2 = com.gpower.coloringbynumber.spf.c.f33083b;
                    cVar2.V1(cVar2.m0() + 1);
                    coloringActivity.showPropsAddAnim(1, coloringActivity.mRewardCategory);
                    break;
                case 3:
                    com.gpower.coloringbynumber.spf.c cVar3 = com.gpower.coloringbynumber.spf.c.f33083b;
                    cVar3.W1(cVar3.n0() + 2);
                    coloringActivity.showPropsAddAnim(2, coloringActivity.mRewardCategory);
                    break;
                case 4:
                    coloringActivity.dispatchSubStatusChange(true);
                    ViewStubFinishViewBinding viewStubFinishViewBinding = coloringActivity.mFinishResultBinding;
                    if (!(viewStubFinishViewBinding == null || (imageView2 = viewStubFinishViewBinding.editPathWaterMaskIcon) == null)) {
                        imageView2.setVisibility(8);
                    }
                    ViewStubFinishViewBinding viewStubFinishViewBinding2 = coloringActivity.mFinishResultBinding;
                    if (!(viewStubFinishViewBinding2 == null || (imageView = viewStubFinishViewBinding2.editPathWaterMaskCloseIcon) == null)) {
                        imageView.setVisibility(8);
                    }
                    BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo = coloringActivity.mRelationBean;
                    if (!(beanResourceRelationTemplateInfo == null || (beanTemplateInfo = beanResourceRelationTemplateInfo.getBeanTemplateInfo()) == null)) {
                        beanTemplateInfo.setRemoveWaterMark(true);
                        y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(coloringActivity), z0.c(), null, new ColoringActivity$showRewardVideo$3$1$1(beanTemplateInfo, null), 2, null);
                        break;
                    }
                    break;
                case 5:
                    coloringActivity.propGiftStartClaimAnim(1);
                    break;
                case 6:
                    coloringActivity.propGiftStartClaimAnim(2);
                    break;
            }
        }
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public final void showSelectColor() {
        ViewStubPaintToolsBinding viewStubPaintToolsBinding;
        ConstraintLayout constraintLayout;
        if (!getMPopupSelectColor().isShowing() && (viewStubPaintToolsBinding = this.mPaintToolsBinding) != null && (constraintLayout = viewStubPaintToolsBinding.activityEditPathBottomLayout) != null) {
            getMPopupSelectColor().a(constraintLayout);
            this.mHandler.sendEmptyMessageDelayed(311, 5000);
        }
    }

    /* access modifiers changed from: private */
    public final void showTransAlphaAnim() {
        y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(this), z0.c(), null, new ColoringActivity$showTransAlphaAnim$1(this, null), 2, null);
    }

    private final void showTransAnim() {
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding;
        CustomPreviewImageView customPreviewImageView;
        int i10;
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding2;
        ClipImageView clipImageView;
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding3;
        CustomPreviewImageView customPreviewImageView2;
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding4;
        ConstraintLayout constraintLayout;
        if (getMShowTrans()) {
            postponeEnterTransition();
            Object mImageUrl = getMImageUrl();
            if (mImageUrl != null) {
                getWindow().getSharedElementEnterTransition().setDuration(1000);
                getWindow().getSharedElementEnterTransition().addListener(new o(this));
                ActivityEditBinding activityEditBinding = this.mBinding;
                if (!(activityEditBinding == null || (layoutPreviewLayoutBinding4 = activityEditBinding.editIncludePreviewLayout) == null || (constraintLayout = layoutPreviewLayoutBinding4.previewImageLayout) == null)) {
                    constraintLayout.setTransitionName(getMDataId());
                }
                ActivityEditBinding activityEditBinding2 = this.mBinding;
                if (!(activityEditBinding2 == null || (layoutPreviewLayoutBinding3 = activityEditBinding2.editIncludePreviewLayout) == null || (customPreviewImageView2 = layoutPreviewLayoutBinding3.previewImage) == null)) {
                    customPreviewImageView2.aspectRatio(getMAspectRatio());
                }
                imageSettingRadio(getMAspectRatio());
                ActivityEditBinding activityEditBinding3 = this.mBinding;
                if (!(activityEditBinding3 == null || (layoutPreviewLayoutBinding2 = activityEditBinding3.editIncludePreviewLayout) == null || (clipImageView = layoutPreviewLayoutBinding2.previewClipImageView) == null)) {
                    if (getMAspectRatio() == 1.0f) {
                        clipImageView.setImageResource(R.drawable.loading00);
                    } else {
                        clipImageView.setImageResource(R.drawable.loading9_00);
                    }
                }
                int d10 = h0.c.f88264a.d() - (com.base.module.tools.model_base.tools.m.b(73.0f) * 2);
                int mAspectRatio = (int) (((float) d10) * getMAspectRatio());
                startPostponedEnterTransition();
                ActivityEditBinding activityEditBinding4 = this.mBinding;
                if (!(activityEditBinding4 == null || (layoutPreviewLayoutBinding = activityEditBinding4.editIncludePreviewLayout) == null || (customPreviewImageView = layoutPreviewLayoutBinding.previewImage) == null)) {
                    RequestBuilder<Bitmap> asBitmap = Glide.with((FragmentActivity) this).asBitmap();
                    if (getMGuessCover() != 0) {
                        mImageUrl = Integer.valueOf(getMGuessCover());
                    }
                    RequestBuilder override = asBitmap.load(mImageUrl).override(d10, mAspectRatio);
                    Object mObjectKey = getMObjectKey();
                    if (mObjectKey == null) {
                        mObjectKey = Long.valueOf(System.currentTimeMillis());
                    }
                    RequestBuilder signature = override.signature(new ObjectKey(mObjectKey));
                    if (getMAspectRatio() == 1.0f) {
                        i10 = R.drawable.ic_placeholder_rounded;
                    } else {
                        i10 = R.drawable.ic_placeholder_rounded_large;
                    }
                    if (signature.placeholder(i10).transform(new CenterCrop(), new RoundedCorners(com.base.module.tools.model_base.tools.m.b(12.0f))).listener(new p(customPreviewImageView)).submit() != null) {
                        return;
                    }
                }
                new Function0() { // from class: com.gpower.coloringbynumber.activity.y0
                    @Override // kotlin.jvm.functions.Function0
                    public final Object invoke() {
                        return ColoringActivity.showTransAnim$lambda$23$lambda$22(ColoringActivity.this);
                    }
                };
                return;
            }
            new Function0() { // from class: com.gpower.coloringbynumber.activity.z0
                @Override // kotlin.jvm.functions.Function0
                public final Object invoke() {
                    return ColoringActivity.showTransAnim$lambda$24(ColoringActivity.this);
                }
            };
            return;
        }
        loadPreView(true);
        loadColorData();
        Unit unit = Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit showTransAnim$lambda$23$lambda$22(ColoringActivity coloringActivity) {
        coloringActivity.loadPreView(true);
        coloringActivity.loadColorData();
        return Unit.f97091a;
    }

    /* access modifiers changed from: private */
    public static final Unit showTransAnim$lambda$24(ColoringActivity coloringActivity) {
        coloringActivity.loadPreView(true);
        coloringActivity.loadColorData();
        return Unit.f97091a;
    }

    private final void skipAnim() {
        if (this.showAnimLoading) {
            dispatchPaintedAnimEnd();
        }
    }

    /* access modifiers changed from: private */
    public final void startFrameAnim() {
        int[] iArr;
        ClipImageView clipImageView;
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding;
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding2;
        ClipImageView clipImageView2;
        if (getMAspectRatio() == 1.0f) {
            iArr = this.mColorAnim;
        } else {
            iArr = this.mLongColorAnim;
        }
        ActivityEditBinding activityEditBinding = this.mBinding;
        if (!(activityEditBinding == null || (layoutPreviewLayoutBinding2 = activityEditBinding.editIncludePreviewLayout) == null || (clipImageView2 = layoutPreviewLayoutBinding2.previewClipImageView) == null)) {
            clipImageView2.setAlpha(1.0f);
        }
        com.gpower.coloringbynumber.tools.f b10 = com.gpower.coloringbynumber.tools.f.b();
        ActivityEditBinding activityEditBinding2 = this.mBinding;
        if (activityEditBinding2 == null || (layoutPreviewLayoutBinding = activityEditBinding2.editIncludePreviewLayout) == null) {
            clipImageView = null;
        } else {
            clipImageView = layoutPreviewLayoutBinding.previewClipImageView;
        }
        f.a a10 = b10.a(clipImageView, iArr);
        this.mFrameAnim = a10;
        if (a10 != null) {
            a10.r(new s(this));
        }
        f.a aVar = this.mFrameAnim;
        if (aVar != null) {
            aVar.o(1500);
        }
        f.a aVar2 = this.mFrameAnim;
        if (aVar2 != null) {
            aVar2.s();
        }
    }

    /* access modifiers changed from: private */
    public final void startPaintFinishAnim(int i10) {
        ColorPaintedView colorPaintedView;
        Boolean bool;
        ViewStubPaintToolsBinding viewStubPaintToolsBinding = this.mPaintToolsBinding;
        if (!(viewStubPaintToolsBinding == null || (colorPaintedView = viewStubPaintToolsBinding.editSingleAnimView) == null)) {
            ObjectAnimator ofFloat = ObjectAnimator.ofFloat(colorPaintedView, "translationX", colorPaintedView.getTranslationX(), (float) h0.c.f88264a.d());
            AnimatorSet animatorSet = this.mAnim;
            if (animatorSet != null) {
                bool = Boolean.valueOf(animatorSet.isRunning());
            } else {
                bool = null;
            }
            if (bool != null) {
                AnimatorSet animatorSet2 = this.mAnim;
                if (animatorSet2 != null) {
                    animatorSet2.cancel();
                }
                this.mAnim = null;
            }
            AnimatorSet animatorSet3 = new AnimatorSet();
            animatorSet3.play(ofFloat);
            animatorSet3.setDuration(200L);
            animatorSet3.setInterpolator(new AccelerateInterpolator());
            animatorSet3.addListener(new t(colorPaintedView));
            animatorSet3.start();
            this.mAnim = animatorSet3;
        }
        y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(this), z0.c(), null, new ColoringActivity$startPaintFinishAnim$2(i10, this, null), 2, null);
    }

    private final void startShareLayout() {
        this.mHandler.sendEmptyMessageDelayed(137, 150);
    }

    /* access modifiers changed from: private */
    public final void startShowGiftAnim(int i10) {
        RecyclerView recyclerView;
        RecyclerView.ViewHolder findViewHolderForLayoutPosition;
        ViewStubPaintToolsBinding viewStubPaintToolsBinding = this.mPaintToolsBinding;
        if (viewStubPaintToolsBinding == null || (recyclerView = viewStubPaintToolsBinding.svgColorListView) == null || (findViewHolderForLayoutPosition = recyclerView.findViewHolderForLayoutPosition(i10)) == null) {
            startSurpriseColorProgress(getMColorListAdapter().getMGiftIcon());
        } else if (findViewHolderForLayoutPosition instanceof PaintColorListAdapter.MyViewHolder) {
            startSurpriseColorProgress(((PaintColorListAdapter.MyViewHolder) findViewHolderForLayoutPosition).getMGiftIconImg());
        }
    }

    private final void startSurpriseColorProgress(View view) {
        LottieAnimationView lottieAnimationView;
        ConstraintLayout constraintLayout;
        createSurpriseColorBinding();
        if (view != null) {
            view.setVisibility(0);
        }
        ViewStubSurpriseColorBinding viewStubSurpriseColorBinding = this.mSurpriseColoringBinding;
        if (!(viewStubSurpriseColorBinding == null || (constraintLayout = viewStubSurpriseColorBinding.surpriseRootLayout) == null)) {
            constraintLayout.setOnClickListener(new View.OnClickListener() { // from class: com.gpower.coloringbynumber.activity.h1
                @Override // android.view.View.OnClickListener
                public final void onClick(View view2) {
                    ColoringActivity.startSurpriseColorProgress$lambda$146(view2);
                }
            });
        }
        ViewStubSurpriseColorBinding viewStubSurpriseColorBinding2 = this.mSurpriseColoringBinding;
        if (viewStubSurpriseColorBinding2 != null && (lottieAnimationView = viewStubSurpriseColorBinding2.surpriseLottieView) != null) {
            lottieAnimationView.post(new Runnable(view) { // from class: com.gpower.coloringbynumber.activity.i1

                /* renamed from: u  reason: collision with root package name */
                public final /* synthetic */ View f32163u;

                {
                    this.f32163u = r2;
                }

                @Override // java.lang.Runnable
                public final void run() {
                    ColoringActivity.startSurpriseColorProgress$lambda$148(ColoringActivity.this, this.f32163u);
                }
            });
        }
    }

    /* access modifiers changed from: private */
    public static final void startSurpriseColorProgress$lambda$146(View view) {
    }

    /* access modifiers changed from: private */
    public static final void startSurpriseColorProgress$lambda$148(ColoringActivity coloringActivity, View view) {
        LottieAnimationView lottieAnimationView;
        ViewStubSurpriseColorBinding viewStubSurpriseColorBinding = coloringActivity.mSurpriseColoringBinding;
        if (viewStubSurpriseColorBinding != null) {
            lottieAnimationView = viewStubSurpriseColorBinding.surpriseLottieView;
        } else {
            lottieAnimationView = null;
        }
        com.gpower.coloringbynumber.tools.e.q(lottieAnimationView, view, 0, new Function0() { // from class: com.gpower.coloringbynumber.activity.x1
            @Override // kotlin.jvm.functions.Function0
            public final Object invoke() {
                return ColoringActivity.startSurpriseColorProgress$lambda$148$lambda$147(ColoringActivity.this);
            }
        }, 4, null);
    }

    /* access modifiers changed from: private */
    public static final Unit startSurpriseColorProgress$lambda$148$lambda$147(ColoringActivity coloringActivity) {
        LottieAnimationView lottieAnimationView;
        LottieAnimationView lottieAnimationView2;
        ViewStubSurpriseColorBinding viewStubSurpriseColorBinding = coloringActivity.mSurpriseColoringBinding;
        if (!(viewStubSurpriseColorBinding == null || (lottieAnimationView2 = viewStubSurpriseColorBinding.surpriseLottieView) == null)) {
            lottieAnimationView2.setVisibility(0);
        }
        ViewStubSurpriseColorBinding viewStubSurpriseColorBinding2 = coloringActivity.mSurpriseColoringBinding;
        if (!(viewStubSurpriseColorBinding2 == null || (lottieAnimationView = viewStubSurpriseColorBinding2.surpriseLottieView) == null)) {
            lottieAnimationView.playAnimation();
        }
        y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(coloringActivity), z0.c(), null, new ColoringActivity$startSurpriseColorProgress$2$1$1(coloringActivity, null), 2, null);
        return Unit.f97091a;
    }

    private final void subscriptionSuccess() {
        BeanTemplateInfoDBM beanTemplateInfo;
        ImageView imageView;
        ImageView imageView2;
        ImageView imageView3;
        FrameLayout frameLayout;
        AppCompatImageView appCompatImageView;
        TextView textView;
        TextView textView2;
        String str;
        TextView textView3;
        CharSequence text;
        ViewStubPaintToolsBinding viewStubPaintToolsBinding = this.mPaintToolsBinding;
        if (!(viewStubPaintToolsBinding == null || (textView2 = viewStubPaintToolsBinding.tvHintNum) == null || textView2.getVisibility() != 0)) {
            ViewStubPaintToolsBinding viewStubPaintToolsBinding2 = this.mPaintToolsBinding;
            if (viewStubPaintToolsBinding2 == null || (textView3 = viewStubPaintToolsBinding2.tvHintNum) == null || (text = textView3.getText()) == null) {
                str = null;
            } else {
                str = text.toString();
            }
            if (Intrinsics.g(str, "AD")) {
                TDEventUtils.a(k2.g.f96925y, 505, "reward");
            }
        }
        propGiftCloseAndRemoveHandler();
        ViewStubPaintToolsBinding viewStubPaintToolsBinding3 = this.mPaintToolsBinding;
        if (!(viewStubPaintToolsBinding3 == null || (textView = viewStubPaintToolsBinding3.tvHintNum) == null)) {
            q0.a(textView, false);
        }
        ViewStubPaintToolsBinding viewStubPaintToolsBinding4 = this.mPaintToolsBinding;
        if (!(viewStubPaintToolsBinding4 == null || (appCompatImageView = viewStubPaintToolsBinding4.ivHintsUnLimited) == null)) {
            q0.a(appCompatImageView, true);
        }
        hideBannerAds();
        ViewStubPaintToolsBinding viewStubPaintToolsBinding5 = this.mPaintToolsBinding;
        if (!(viewStubPaintToolsBinding5 == null || (frameLayout = viewStubPaintToolsBinding5.bannerAdRl) == null)) {
            q0.b(frameLayout, false);
        }
        ViewStubFinishViewBinding viewStubFinishViewBinding = this.mFinishResultBinding;
        if (!(viewStubFinishViewBinding == null || (imageView = viewStubFinishViewBinding.editPathWaterMaskIcon) == null || imageView.getVisibility() != 0)) {
            ViewStubFinishViewBinding viewStubFinishViewBinding2 = this.mFinishResultBinding;
            if (!(viewStubFinishViewBinding2 == null || (imageView3 = viewStubFinishViewBinding2.editPathWaterMaskIcon) == null)) {
                q0.a(imageView3, false);
            }
            ViewStubFinishViewBinding viewStubFinishViewBinding3 = this.mFinishResultBinding;
            if (!(viewStubFinishViewBinding3 == null || (imageView2 = viewStubFinishViewBinding3.editPathWaterMaskCloseIcon) == null)) {
                q0.a(imageView2, false);
            }
        }
        BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo = this.mRelationBean;
        if (beanResourceRelationTemplateInfo != null && (beanTemplateInfo = beanResourceRelationTemplateInfo.getBeanTemplateInfo()) != null) {
            beanTemplateInfo.setRemoveWaterMark(true);
            y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(this), z0.c(), null, new ColoringActivity$subscriptionSuccess$1$1(beanTemplateInfo, null), 2, null);
        }
    }

    private final void tdEventOutBackground(long j10) {
        TDEventUtils.f33166a.n(k2.d.L, k2.e.f96840y, String.valueOf(((float) j10) / 1000.0f));
    }

    private final void transitionElementCallBack() {
        setEnterSharedElementCallback(new SharedElementCallback(this) { // from class: com.gpower.coloringbynumber.activity.ColoringActivity$transitionElementCallBack$1
            final /* synthetic */ ColoringActivity this$0;

            {
                this.this$0 = r1;
            }

            @Override // androidx.core.app.SharedElementCallback
            public void onMapSharedElements(List<String> list, Map<String, View> map) {
                ActivityEditBinding mBinding;
                FrameLayout frameLayout;
                String mDataId;
                if (!((this.this$0.getCurPaintStage() != 2 && this.this$0.getCurPaintStage() != 3) || (mBinding = this.this$0.getMBinding()) == null || (frameLayout = mBinding.editFinishAnimLayout) == null || (mDataId = this.this$0.getMDataId()) == null || map == null)) {
                    map.put(mDataId, frameLayout);
                }
                super.onMapSharedElements(list, map);
            }

            @Override // androidx.core.app.SharedElementCallback
            public void onSharedElementsArrived(List<String> list, List<View> list2, SharedElementCallback.OnSharedElementsReadyListener onSharedElementsReadyListener) {
                super.onSharedElementsArrived(list, list2, onSharedElementsReadyListener);
            }
        });
    }

    public final void checkPathCount(Float f10) {
        float f11;
        String str;
        if (!this.startPainted) {
            this.startPainted = true;
            AdsActivityManager.f33023a.o(k2.f.f96844c);
        }
        this.propsUnUse = false;
        this.mPathPaintCount++;
        propGiftRestartCalculationTime(1);
        ViewModelAchievement i10 = App.I.b().i();
        if (i10 != null) {
            i10.achievementTypeAddProgress(2);
        }
        if (!this.eventReportProgress50) {
            if (f10 != null) {
                f11 = f10.floatValue();
            } else {
                f11 = ((float) this.mPathPaintCount) / ((float) this.mPathTotalCount);
            }
            if (f11 > 0.5f) {
                TDEventUtils tDEventUtils = TDEventUtils.f33166a;
                BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo = this.mRelationBean;
                if (beanResourceRelationTemplateInfo == null || (str = beanResourceRelationTemplateInfo.itemCode()) == null) {
                    str = "";
                }
                tDEventUtils.n(k2.d.X, k2.e.f96825j, str);
                this.eventReportProgress50 = true;
            }
        }
        int size = getMColorListAdapter().getMList().size();
        int selectPosition = getMColorListAdapter().getSelectPosition();
        if (selectPosition >= 0 && selectPosition < size) {
            BeanPaintColor beanPaintColor = getMColorListAdapter().getMList().get(getMColorListAdapter().getSelectPosition());
            SvgColorInfo mSvgColorItem = beanPaintColor.getMSvgColorItem();
            if (mSvgColorItem != null) {
                mSvgColorItem.setPaintCount(mSvgColorItem.getPaintCount() + 1);
                refreshRecyclerViewPosition$default(this, 0, 1, null);
                getMColorListAdapter().refreshPaintInfo();
                if (mSvgColorItem.getPaintCount() == mSvgColorItem.getTotalCount()) {
                    scrollRecyclerToPosition(getMColorListAdapter().getSelectPosition());
                    startVibrator();
                    MediaPlayer mediaPlayer = this.soundMediaPlayer;
                    if (mediaPlayer != null) {
                        mediaPlayer.start();
                    }
                }
            }
            ColorInformation mBlockColorItem = beanPaintColor.getMBlockColorItem();
            if (mBlockColorItem != null) {
                refreshRecyclerViewPosition$default(this, 0, 1, null);
                getMColorListAdapter().refreshPaintInfo();
                if (mBlockColorItem.getNumberPainted() == mBlockColorItem.getBlockCount()) {
                    scrollRecyclerToPosition(getMColorListAdapter().getSelectPosition());
                    startVibrator();
                    MediaPlayer mediaPlayer2 = this.soundMediaPlayer;
                    if (mediaPlayer2 != null) {
                        mediaPlayer2.start();
                    }
                }
            }
        }
    }

    public void dispatchAutoPaint(long j10) {
        CustomAutoPaintProgressBar customAutoPaintProgressBar;
        ViewStubPaintToolsBinding viewStubPaintToolsBinding = this.mPaintToolsBinding;
        if (viewStubPaintToolsBinding != null && (customAutoPaintProgressBar = viewStubPaintToolsBinding.progressAutoAnim) != null) {
            customAutoPaintProgressBar.e(j10, new Function0() { // from class: com.gpower.coloringbynumber.activity.r1
                @Override // kotlin.jvm.functions.Function0
                public final Object invoke() {
                    return ColoringActivity.dispatchAutoPaint$lambda$150(ColoringActivity.this);
                }
            });
        }
    }

    public abstract void dispatchAutoPaint2();

    public abstract void dispatchCurProgressPaintBitmap(Function1<? super Bitmap, Unit> function1);

    public abstract int dispatchCurSelectPathId();

    public abstract void dispatchDownloadVideo();

    public abstract void dispatchHideLineAnim(boolean z10, Function0<Unit> function0);

    public abstract void dispatchHidePaintView();

    public abstract void dispatchInitAnimFinish();

    public abstract void dispatchLike(BeanTemplateInfoDBM beanTemplateInfoDBM);

    public abstract void dispatchLoadTemplateInfo(String str, boolean z10);

    public abstract void dispatchOneClickColoring();

    public abstract void dispatchPaintListenerEvent();

    public abstract void dispatchPaintView();

    public abstract void dispatchPaintedAnimEnd();

    public abstract void dispatchPaintedFinish(float f10, float f11);

    public abstract void dispatchRecycleResource();

    public abstract void dispatchSaveDrawInfoToDB(int i10);

    public abstract Object dispatchSaveTemplatePic(Function2<? super Boolean, ? super Continuation<? super Unit>, ? extends Object> function2, Continuation<? super Unit> continuation);

    public abstract void dispatchScaleZoomDefault(Function0<Unit> function0);

    public abstract void dispatchSelectedColorId(int i10, boolean z10);

    public abstract void dispatchStartPaintAnim();

    public abstract void dispatchSubStatusChange(boolean z10);

    public abstract Object dispatchUnFinishInitCreateTemplate(String str, Continuation<? super BeanTemplateInfoDBM> continuation);

    public abstract void dispatchVideoDownloadCancel();

    public abstract void dispatchZoomCurSelectArea(long j10, Float f10);

    @Override // com.gpower.coloringbynumber.activity.BaseActivity, android.app.Activity
    public void finish() {
        super.finish();
        com.gpower.coloringbynumber.spf.c cVar = com.gpower.coloringbynumber.spf.c.f33083b;
        if (cVar.o0()) {
            cVar.X1(false);
        }
        this.exitDelay = 0;
        Runnable runnable = this.mSendRefreshRunnable;
        if (runnable != null) {
            runnable.run();
        }
        this.mSendRefreshRunnable = null;
        AnimatorSet animatorSet = this.mAnimSet;
        if (animatorSet != null) {
            animatorSet.cancel();
        }
        this.mAnimSet = null;
    }

    public final int getBlockNumber() {
        return this.blockNumber;
    }

    public final String getCategoryId() {
        return this.categoryId;
    }

    public final String getCategoryName() {
        return this.categoryName;
    }

    public final int getColorNumber() {
        return this.colorNumber;
    }

    public final int getCurPaintStage() {
        return this.curPaintStage;
    }

    public final int getDrawDiffLevel() {
        return this.drawDiffLevel;
    }

    public final float getEntityHeight() {
        return this.entityHeight;
    }

    public final float getEntityWidth() {
        return this.entityWidth;
    }

    public final boolean getEventReportProgress50() {
        return this.eventReportProgress50;
    }

    public final long getExitDelay() {
        return this.exitDelay;
    }

    public final int getLineNumber() {
        return this.lineNumber;
    }

    public final boolean getLoading() {
        return this.loading;
    }

    public final String getMAdvPosition() {
        return this.mAdvPosition;
    }

    public final ActivityEditBinding getMBinding() {
        return this.mBinding;
    }

    public final PaintColorListAdapter getMColorListAdapter() {
        return (PaintColorListAdapter) this.mColorListAdapter$delegate.getValue();
    }

    public final l0.b getMColorListener() {
        return this.mColorListener;
    }

    public final String getMDataId() {
        return (String) this.mDataId$delegate.getValue();
    }

    public final ViewStubPaintToolsBinding getMPaintToolsBinding() {
        return this.mPaintToolsBinding;
    }

    public final int getMPathPaintCount() {
        return this.mPathPaintCount;
    }

    public final int getMPathTotalCount() {
        return this.mPathTotalCount;
    }

    public final PopSelectColor getMPopupSelectColor() {
        return (PopSelectColor) this.mPopupSelectColor$delegate.getValue();
    }

    public final BeanResourceRelationTemplateInfo getMRelationBean() {
        return this.mRelationBean;
    }

    public final RewardCategory getMRewardCategory() {
        return this.mRewardCategory;
    }

    public final Runnable getMSendRefreshRunnable() {
        return this.mSendRefreshRunnable;
    }

    public final String getMSvgName() {
        return this.mSvgName;
    }

    public final String getMSvgNameVersion() {
        return this.mSvgNameVersion;
    }

    public final int getMThisTimesIntoCount() {
        return this.mThisTimesIntoCount;
    }

    public final boolean getMV25Data() {
        return ((Boolean) this.mV25Data$delegate.getValue()).booleanValue();
    }

    public final LayoutGuessColoringTipsBinding getMVsGuessTipsBinding() {
        return this.mVsGuessTipsBinding;
    }

    public final boolean getRefreshFlowResult() {
        return this.refreshFlowResult;
    }

    @Override // com.gpower.coloringbynumber.activity.BaseActivity
    public void handleAppMessage(Message message) {
        ImageView imageView;
        ImageView imageView2;
        ConstraintLayout constraintLayout;
        Intrinsics.checkNotNullParameter(message, "msg");
        int i10 = message.what;
        if (i10 == 137) {
            ActivityEditBinding activityEditBinding = this.mBinding;
            if (activityEditBinding != null && (imageView = activityEditBinding.idBack) != null && imageView.getVisibility() == 0) {
                ActivityEditBinding activityEditBinding2 = this.mBinding;
                if (activityEditBinding2 != null) {
                    imageView2 = activityEditBinding2.idBack;
                } else {
                    imageView2 = null;
                }
                AnimUtilsKt.t(imageView2, 1000, 0, 0.0f, null, null, null, 124, null);
            }
        } else if (i10 == 156) {
            startShareLayout();
        } else if (i10 == 158) {
            com.gpower.coloringbynumber.pop.s sVar = this.mPopupDownload;
            if (sVar != null) {
                sVar.d();
            }
            Toast.makeText(this, getString(R.string.save_failed), 0).show();
        } else if (i10 == 163) {
            LayoutGuessColoringTipsBinding layoutGuessColoringTipsBinding = this.mVsGuessTipsBinding;
            if (layoutGuessColoringTipsBinding != null && (constraintLayout = layoutGuessColoringTipsBinding.clGuessTips) != null) {
                q0.a(constraintLayout, false);
            }
        } else if (i10 == 311) {
            getMPopupSelectColor().dismiss();
        } else if (i10 == 321) {
            propGiftShowPopupWindow();
        }
    }

    @Override // com.gpower.coloringbynumber.activity.BaseActivity
    public void initData() {
        this.mCalendarIdList.clear();
        this.mCollectIdList.clear();
        y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(this), z0.c(), null, new ColoringActivity$initData$1(this, null), 2, null);
        getMLikeViewModel().getTemplateLiveData().observe(this, new k(new Function1() { // from class: com.gpower.coloringbynumber.activity.r0
            @Override // kotlin.jvm.functions.Function1
            public final Object invoke(Object obj) {
                return ColoringActivity.initData$lambda$37(ColoringActivity.this, (BeanTemplateInfoDBM) obj);
            }
        }));
        getMColorViewModel().getMBeanCategoryLiveData().observe(this, new k(new Function1() { // from class: com.gpower.coloringbynumber.activity.s0
            @Override // kotlin.jvm.functions.Function1
            public final Object invoke(Object obj) {
                return ColoringActivity.initData$lambda$38(ColoringActivity.this, (V25BeanCategoryDBM) obj);
            }
        }));
    }

    @Override // com.gpower.coloringbynumber.activity.BaseActivity
    public void initLayout() {
        ConstraintLayout constraintLayout;
        TDEventUtils.f33166a.n("loading_animation", new Object[0]);
        ActivityEditBinding inflate = ActivityEditBinding.inflate(getLayoutInflater());
        this.mBinding = inflate;
        if (inflate != null) {
            constraintLayout = inflate.getRoot();
        } else {
            constraintLayout = null;
        }
        setContentView(constraintLayout);
    }

    public final void initSuccessViewCreate() {
        ViewStub viewStub;
        View inflate;
        ActivityEditBinding activityEditBinding;
        ConstraintLayout constraintLayout;
        FrameLayout frameLayout;
        RecyclerViewTopAnimView recyclerViewTopAnimView;
        ViewStubPaintToolsBinding viewStubPaintToolsBinding;
        ViewStub viewStub2;
        View inflate2;
        ViewStub viewStub3;
        LayoutGuessColoringTipsBinding layoutGuessColoringTipsBinding = null;
        if (this.mPaintToolsBinding == null) {
            ActivityEditBinding activityEditBinding2 = this.mBinding;
            if (!(activityEditBinding2 == null || (viewStub3 = activityEditBinding2.viewStubPaintToolsLayout) == null)) {
                viewStub3.setOnInflateListener(new ViewStub.OnInflateListener() { // from class: com.gpower.coloringbynumber.activity.u1
                    @Override // android.view.ViewStub.OnInflateListener
                    public final void onInflate(ViewStub viewStub4, View view) {
                        ColoringActivity.initSuccessViewCreate$lambda$39(ColoringActivity.this, viewStub4, view);
                    }
                });
            }
            ActivityEditBinding activityEditBinding3 = this.mBinding;
            if (activityEditBinding3 == null || (viewStub2 = activityEditBinding3.viewStubPaintToolsLayout) == null || (inflate2 = viewStub2.inflate()) == null) {
                viewStubPaintToolsBinding = null;
            } else {
                viewStubPaintToolsBinding = ViewStubPaintToolsBinding.bind(inflate2);
            }
            this.mPaintToolsBinding = viewStubPaintToolsBinding;
        }
        ViewStubPaintToolsBinding viewStubPaintToolsBinding2 = this.mPaintToolsBinding;
        if (viewStubPaintToolsBinding2 != null) {
            viewStubPaintToolsBinding2.btnScale.setOnClickListener(this);
            viewStubPaintToolsBinding2.ivHintIcon.setOnClickListener(this);
            viewStubPaintToolsBinding2.ivRemoveAdv.setOnClickListener(this);
            viewStubPaintToolsBinding2.ivAutoImg.setOnClickListener(this);
            RecyclerView.ItemAnimator itemAnimator = viewStubPaintToolsBinding2.svgColorListView.getItemAnimator();
            if (itemAnimator != null) {
                itemAnimator.setChangeDuration(0);
            }
            LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, 0, false);
            this.mColorLayoutManager = linearLayoutManager;
            viewStubPaintToolsBinding2.svgColorListView.setLayoutManager(linearLayoutManager);
            viewStubPaintToolsBinding2.svgColorListView.setAdapter(getMColorListAdapter());
            viewStubPaintToolsBinding2.svgColorListView.setOnScrollChangeListener(new View.OnScrollChangeListener() { // from class: com.gpower.coloringbynumber.activity.v1
                @Override // android.view.View.OnScrollChangeListener
                public final void onScrollChange(View view, int i10, int i11, int i12, int i13) {
                    ColoringActivity.initSuccessViewCreate$lambda$42$lambda$41(ColoringActivity.this, view, i10, i11, i12, i13);
                }
            });
            setMAutoPaintFindNum(com.gpower.coloringbynumber.spf.c.f33083b.m0());
        }
        ViewStubPaintToolsBinding viewStubPaintToolsBinding3 = this.mPaintToolsBinding;
        if (!(viewStubPaintToolsBinding3 == null || (recyclerViewTopAnimView = viewStubPaintToolsBinding3.editColoringAnimView) == null)) {
            recyclerViewTopAnimView.setShowDraw(true);
        }
        if (!(!h0.c.f88264a.e() || (activityEditBinding = this.mBinding) == null || (constraintLayout = activityEditBinding.editTotalLayout) == null)) {
            if (activityEditBinding != null) {
                frameLayout = activityEditBinding.editFinishAnimLayout;
            } else {
                frameLayout = null;
            }
            constraintLayout.removeView(frameLayout);
        }
        BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo = this.mRelationBean;
        if (beanResourceRelationTemplateInfo != null && beanResourceRelationTemplateInfo.getShowGuessTips()) {
            if (this.mVsGuessTipsBinding == null) {
                ActivityEditBinding activityEditBinding4 = this.mBinding;
                if (!(activityEditBinding4 == null || (viewStub = activityEditBinding4.vsGuessTips) == null || (inflate = viewStub.inflate()) == null)) {
                    layoutGuessColoringTipsBinding = LayoutGuessColoringTipsBinding.bind(inflate);
                }
                this.mVsGuessTipsBinding = layoutGuessColoringTipsBinding;
            }
            this.mHandler.sendEmptyMessageDelayed(163, 3000);
        }
    }

    @Override // com.gpower.coloringbynumber.activity.BaseActivity
    public void initView() {
        IncludeNetworkErrorViewBinding includeNetworkErrorViewBinding;
        Button button;
        ImageView imageView;
        ActivityEditBinding activityEditBinding;
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding;
        ConstraintLayout constraintLayout;
        h0.c cVar = h0.c.f88264a;
        if (!cVar.e()) {
            getWindow().setEnterTransition(TransitionInflater.from(this).inflateTransition(R.transition.activity_fade_in));
            transitionElementCallBack();
        }
        if (!(!cVar.b() || (activityEditBinding = this.mBinding) == null || (layoutPreviewLayoutBinding = activityEditBinding.editIncludePreviewLayout) == null || (constraintLayout = layoutPreviewLayoutBinding.previewImageLayout) == null)) {
            ViewGroup.LayoutParams layoutParams = constraintLayout.getLayoutParams();
            if (layoutParams != null) {
                ConstraintLayout.LayoutParams layoutParams2 = (ConstraintLayout.LayoutParams) layoutParams;
                ((ViewGroup.MarginLayoutParams) layoutParams2).width = (int) (((float) cVar.d()) / 2.0f);
                constraintLayout.setLayoutParams(layoutParams2);
            } else {
                throw new NullPointerException("null cannot be cast to non-null type androidx.constraintlayout.widget.ConstraintLayout.LayoutParams");
            }
        }
        ActivityEditBinding activityEditBinding2 = this.mBinding;
        if (!(activityEditBinding2 == null || (imageView = activityEditBinding2.idBack) == null)) {
            imageView.setOnClickListener(this);
        }
        ActivityEditBinding activityEditBinding3 = this.mBinding;
        if (!(activityEditBinding3 == null || (includeNetworkErrorViewBinding = activityEditBinding3.editIncludeErrorLayout) == null || (button = includeNetworkErrorViewBinding.btnTryAgain) == null)) {
            button.setOnClickListener(this);
        }
        showTransAnim();
        initInfoLoad();
    }

    public final boolean isBoughtCollectPackage() {
        return this.isBoughtCollectPackage;
    }

    public final boolean isFinished() {
        return this.isFinished;
    }

    public final boolean isHobbyCollection() {
        return this.isHobbyCollection;
    }

    public final boolean isLike() {
        return this.isLike;
    }

    @Override // com.gpower.coloringbynumber.iap.PurchaseUtil.a, o2.j
    public void onAcknowledgeItemResult(int i10, String str) {
        super.onAcknowledgeItemResult(i10, str);
        if (i10 == 0 && this.showTd && Intrinsics.g(com.gpower.coloringbynumber.spf.c.f33083b.S0(), "week_new")) {
            TDEventUtils.f33166a.n(k2.d.f96792k, "location", k2.g.f96871g, k2.e.f96821f, k2.g.f96856b);
        }
    }

    @Override // com.gpower.coloringbynumber.activity.BaseActivity, androidx.appcompat.app.AppCompatActivity, androidx.fragment.app.FragmentActivity, android.app.Activity
    public void onDestroy() {
        LottieAnimationView lottieAnimationView;
        LottieAnimationView lottieAnimationView2;
        LottieAnimationView lottieAnimationView3;
        LottieAnimationView lottieAnimationView4;
        LottieAnimationView lottieAnimationView5;
        ImageView imageView;
        TextView textView;
        TextView textView2;
        q2.a aVar = q2.a.f99906a;
        ViewStubSurpriseColorBinding viewStubSurpriseColorBinding = this.mSurpriseColoringBinding;
        CharSequence charSequence = null;
        if (viewStubSurpriseColorBinding != null) {
            lottieAnimationView = viewStubSurpriseColorBinding.surpriseLottieView;
        } else {
            lottieAnimationView = null;
        }
        aVar.b(lottieAnimationView);
        ViewStubFinishViewBinding viewStubFinishViewBinding = this.mFinishResultBinding;
        if (viewStubFinishViewBinding != null) {
            lottieAnimationView2 = viewStubFinishViewBinding.editPathFinishTitleLottie;
        } else {
            lottieAnimationView2 = null;
        }
        aVar.b(lottieAnimationView2);
        ViewStubFinishViewBinding viewStubFinishViewBinding2 = this.mFinishResultBinding;
        if (viewStubFinishViewBinding2 != null) {
            lottieAnimationView3 = viewStubFinishViewBinding2.paintFinishAnimation;
        } else {
            lottieAnimationView3 = null;
        }
        aVar.b(lottieAnimationView3);
        ViewStubFinishViewBinding viewStubFinishViewBinding3 = this.mFinishResultBinding;
        if (viewStubFinishViewBinding3 != null) {
            lottieAnimationView4 = viewStubFinishViewBinding3.lottieLike;
        } else {
            lottieAnimationView4 = null;
        }
        aVar.b(lottieAnimationView4);
        ViewStubFinishViewBinding viewStubFinishViewBinding4 = this.mFinishResultBinding;
        if (viewStubFinishViewBinding4 != null) {
            lottieAnimationView5 = viewStubFinishViewBinding4.editPathResultSecondLottie;
        } else {
            lottieAnimationView5 = null;
        }
        aVar.b(lottieAnimationView5);
        hideBannerAds();
        pvEndExitTemplate();
        ge.c.f().A(this);
        dispatchRecycleResource();
        tdEventOutEdit();
        AdsActivityManager.f33023a.q();
        this.exitDelay = 0;
        Runnable runnable = this.mSendRefreshRunnable;
        if (runnable != null) {
            runnable.run();
        }
        this.mSendRefreshRunnable = null;
        ExoPlayer exoPlayer = this.mPlayer;
        if (exoPlayer != null) {
            exoPlayer.release();
        }
        this.mPlayer = null;
        MediaPlayer mediaPlayer = this.soundMediaPlayer;
        if (mediaPlayer != null) {
            mediaPlayer.release();
        }
        this.soundMediaPlayer = null;
        ViewStubPaintToolsBinding viewStubPaintToolsBinding = this.mPaintToolsBinding;
        if (!(viewStubPaintToolsBinding == null || (textView = viewStubPaintToolsBinding.tvHintNum) == null || textView.getVisibility() != 0)) {
            ViewStubPaintToolsBinding viewStubPaintToolsBinding2 = this.mPaintToolsBinding;
            if (!(viewStubPaintToolsBinding2 == null || (textView2 = viewStubPaintToolsBinding2.tvHintNum) == null)) {
                charSequence = textView2.getText();
            }
            if (Intrinsics.g(charSequence, "AD")) {
                TDEventUtils.a(k2.g.f96925y, 505, "reward");
            }
        }
        ViewStubFinishViewBinding viewStubFinishViewBinding5 = this.mFinishResultBinding;
        if (viewStubFinishViewBinding5 != null && (imageView = viewStubFinishViewBinding5.editPathWaterMaskIcon) != null && imageView.getVisibility() == 0 && !Intrinsics.g(com.gpower.coloringbynumber.spf.c.f33083b.r(), o0.a.f99347a.b())) {
            TDEventUtils.a("watermark", 505, "reward");
        }
        super.onDestroy();
        System.gc();
    }

    @Override // androidx.appcompat.app.AppCompatActivity, android.app.Activity, android.view.KeyEvent.Callback
    public boolean onKeyDown(int i10, KeyEvent keyEvent) {
        if (i10 == 4 && keyEvent != null && keyEvent.getRepeatCount() == 0) {
            return false;
        }
        return super.onKeyDown(i10, keyEvent);
    }

    @Override // com.gpower.coloringbynumber.activity.BaseActivity, androidx.fragment.app.FragmentActivity, android.app.Activity
    public void onPause() {
        super.onPause();
        this.curStatus = false;
        ExoPlayer exoPlayer = this.mPlayer;
        if (exoPlayer != null) {
            exoPlayer.setPlayWhenReady(false);
        }
    }

    @ge.l(threadMode = ThreadMode.MAIN)
    public final void onPurchaseFinish(PurchaseEvent purchaseEvent) {
        FrameLayout frameLayout;
        TextView textView;
        String str;
        TextView textView2;
        CharSequence text;
        Intrinsics.checkNotNullParameter(purchaseEvent, "event");
        String type = purchaseEvent.getType();
        int hashCode = type.hashCode();
        if (hashCode != -1125461359) {
            if (hashCode != -1124550816) {
                if (hashCode == -678252391 && type.equals(com.gpower.coloringbynumber.iap.b.f32767k)) {
                    ViewStubPaintToolsBinding viewStubPaintToolsBinding = this.mPaintToolsBinding;
                    if (!(viewStubPaintToolsBinding == null || (textView = viewStubPaintToolsBinding.tvHintNum) == null || textView.getVisibility() != 0)) {
                        ViewStubPaintToolsBinding viewStubPaintToolsBinding2 = this.mPaintToolsBinding;
                        if (viewStubPaintToolsBinding2 == null || (textView2 = viewStubPaintToolsBinding2.tvHintNum) == null || (text = textView2.getText()) == null) {
                            str = null;
                        } else {
                            str = text.toString();
                        }
                        if (Intrinsics.g(str, "AD")) {
                            TDEventUtils.a(k2.g.f96925y, 505, "reward");
                        }
                    }
                    getGoodsBoughtViewModel().queryGoodsBought(PurchaseUtil.GOODS_TYPE_ID_COUPON_HINT, PurchaseUtil.GOODS_TYPE_ID_COUPON_HINT);
                }
            } else if (type.equals(com.gpower.coloringbynumber.iap.b.f32762f)) {
                hideBannerAds();
                ViewStubPaintToolsBinding viewStubPaintToolsBinding3 = this.mPaintToolsBinding;
                if (viewStubPaintToolsBinding3 != null && (frameLayout = viewStubPaintToolsBinding3.bannerAdRl) != null) {
                    q0.b(frameLayout, false);
                }
            }
        } else if (type.equals(com.gpower.coloringbynumber.iap.b.f32759c)) {
            subscriptionSuccess();
        }
    }

    @Override // android.app.Activity
    public void onRestart() {
        super.onRestart();
    }

    @Override // androidx.fragment.app.FragmentActivity, android.app.Activity
    public void onResume() {
        super.onResume();
        this.curStatus = true;
        ExoPlayer exoPlayer = this.mPlayer;
        if (exoPlayer != null) {
            exoPlayer.setPlayWhenReady(true);
        }
        initOrShowBannerAd();
    }

    @Override // androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    public void onSaveInstanceState(Bundle bundle) {
        Intrinsics.checkNotNullParameter(bundle, "outState");
        super.onSaveInstanceState(bundle);
        dispatchSaveDrawInfoToDB(3);
    }

    public final void onSaveVideoError() {
        this.mIsGenerateVideo = false;
        this.mHandler.sendEmptyMessage(158);
    }

    public final void onSaveVideoProgress(int i10) {
        com.gpower.coloringbynumber.pop.s sVar = this.mPopupDownload;
        if (sVar != null) {
            com.gpower.coloringbynumber.pop.s.q(sVar, i10, false, 2, null);
        }
    }

    public final void onSaveVideoSuccess() {
        y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(this), z0.e(), null, new ColoringActivity$onSaveVideoSuccess$1(this, null), 2, null);
    }

    @Override // androidx.appcompat.app.AppCompatActivity, androidx.fragment.app.FragmentActivity, android.app.Activity
    public void onStart() {
        super.onStart();
        if (this.openEditTimeStamp != null) {
            lastOpenEditTime();
        }
    }

    @Override // androidx.appcompat.app.AppCompatActivity, androidx.fragment.app.FragmentActivity, android.app.Activity
    public void onStop() {
        super.onStop();
        Long l10 = this.openEditTimeStamp;
        if (l10 != null) {
            long currentTimeMillis = this.openEditTimeDuration + (System.currentTimeMillis() - l10.longValue());
            this.openEditTimeDuration = currentTimeMillis;
            tdEventOutBackground(currentTimeMillis);
        }
    }

    public final void resultAnimFinish(RectF rectF) {
        Intrinsics.checkNotNullParameter(rectF, "mRectF");
        y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(this), z0.c(), null, new ColoringActivity$resultAnimFinish$1(this, rectF, null), 2, null);
    }

    public final void setBlockNumber(int i10) {
        this.blockNumber = i10;
    }

    public final void setBoughtCollectPackage(boolean z10) {
        this.isBoughtCollectPackage = z10;
    }

    public final void setCategoryId(String str) {
        Intrinsics.checkNotNullParameter(str, "<set-?>");
        this.categoryId = str;
    }

    public final void setCategoryName(String str) {
        Intrinsics.checkNotNullParameter(str, "<set-?>");
        this.categoryName = str;
    }

    public final void setColorNumber(int i10) {
        this.colorNumber = i10;
    }

    public final void setCurPaintStage(int i10) {
        this.curPaintStage = i10;
    }

    public final void setDrawDiffLevel(int i10) {
        this.drawDiffLevel = i10;
    }

    public final void setEntityHeight(float f10) {
        this.entityHeight = f10;
    }

    public final void setEntityWidth(float f10) {
        this.entityWidth = f10;
    }

    public final void setEventReportProgress50(boolean z10) {
        this.eventReportProgress50 = z10;
    }

    public final void setExitDelay(long j10) {
        this.exitDelay = j10;
    }

    public final void setFinished(boolean z10) {
        this.isFinished = z10;
    }

    public final void setHobbyCollection(boolean z10) {
        this.isHobbyCollection = z10;
    }

    public final void setLike(boolean z10) {
        this.isLike = z10;
    }

    public final void setLineNumber(int i10) {
        this.lineNumber = i10;
    }

    public final void setLoading(boolean z10) {
        this.loading = z10;
    }

    public final void setMAdvPosition(String str) {
        Intrinsics.checkNotNullParameter(str, "<set-?>");
        this.mAdvPosition = str;
    }

    public final void setMBinding(ActivityEditBinding activityEditBinding) {
        this.mBinding = activityEditBinding;
    }

    public final void setMPaintToolsBinding(ViewStubPaintToolsBinding viewStubPaintToolsBinding) {
        this.mPaintToolsBinding = viewStubPaintToolsBinding;
    }

    public final void setMPathPaintCount(int i10) {
        this.mPathPaintCount = i10;
    }

    public final void setMPathTotalCount(int i10) {
        this.mPathTotalCount = i10;
    }

    public final void setMRelationBean(BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo) {
        this.mRelationBean = beanResourceRelationTemplateInfo;
    }

    public final void setMRewardCategory(RewardCategory rewardCategory) {
        Intrinsics.checkNotNullParameter(rewardCategory, "<set-?>");
        this.mRewardCategory = rewardCategory;
    }

    public final void setMSendRefreshRunnable(Runnable runnable) {
        this.mSendRefreshRunnable = runnable;
    }

    public final void setMSvgName(String str) {
        Intrinsics.checkNotNullParameter(str, "<set-?>");
        this.mSvgName = str;
    }

    public final void setMSvgNameVersion(String str) {
        Intrinsics.checkNotNullParameter(str, "<set-?>");
        this.mSvgNameVersion = str;
    }

    public final void setMThisTimesIntoCount(int i10) {
        this.mThisTimesIntoCount = i10;
    }

    public final void setMVsGuessTipsBinding(LayoutGuessColoringTipsBinding layoutGuessColoringTipsBinding) {
        this.mVsGuessTipsBinding = layoutGuessColoringTipsBinding;
    }

    public final void setRefreshFlowResult(boolean z10) {
        this.refreshFlowResult = z10;
    }

    public final void showErrorView() {
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding;
        ConstraintLayout constraintLayout;
        IncludeNetworkErrorViewBinding includeNetworkErrorViewBinding;
        ConstraintLayout constraintLayout2;
        ActivityEditBinding activityEditBinding = this.mBinding;
        if (!(activityEditBinding == null || (includeNetworkErrorViewBinding = activityEditBinding.editIncludeErrorLayout) == null || (constraintLayout2 = includeNetworkErrorViewBinding.errorView) == null)) {
            constraintLayout2.setVisibility(0);
        }
        ActivityEditBinding activityEditBinding2 = this.mBinding;
        if (activityEditBinding2 != null && (layoutPreviewLayoutBinding = activityEditBinding2.editIncludePreviewLayout) != null && (constraintLayout = layoutPreviewLayoutBinding.previewRootLayout) != null) {
            constraintLayout.setVisibility(8);
        }
    }

    public final void showGuessTips() {
        ConstraintLayout constraintLayout;
        BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo = this.mRelationBean;
        if (beanResourceRelationTemplateInfo != null && beanResourceRelationTemplateInfo.getShowGuessTips()) {
            this.mHandler.sendEmptyMessageDelayed(163, 3000);
            LayoutGuessColoringTipsBinding layoutGuessColoringTipsBinding = this.mVsGuessTipsBinding;
            if (layoutGuessColoringTipsBinding != null && (constraintLayout = layoutGuessColoringTipsBinding.clGuessTips) != null) {
                q0.a(constraintLayout, true);
            }
        }
    }

    public final void showProgressBar() {
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding;
        ConstraintLayout constraintLayout;
        IncludeNetworkErrorViewBinding includeNetworkErrorViewBinding;
        ConstraintLayout constraintLayout2;
        ActivityEditBinding activityEditBinding = this.mBinding;
        if (!(activityEditBinding == null || (includeNetworkErrorViewBinding = activityEditBinding.editIncludeErrorLayout) == null || (constraintLayout2 = includeNetworkErrorViewBinding.errorView) == null)) {
            constraintLayout2.setVisibility(8);
        }
        ActivityEditBinding activityEditBinding2 = this.mBinding;
        if (activityEditBinding2 != null && (layoutPreviewLayoutBinding = activityEditBinding2.editIncludePreviewLayout) != null && (constraintLayout = layoutPreviewLayoutBinding.previewRootLayout) != null) {
            constraintLayout.setVisibility(0);
        }
    }

    public final void showRefreshRunnable() {
        if (!getMShowTrans() || (com.gpower.coloringbynumber.spf.c.f33083b.V() && this.curPaintStage == 3)) {
            Runnable runnable = this.mSendRefreshRunnable;
            if (runnable != null) {
                runnable.run();
            }
            this.mSendRefreshRunnable = null;
        }
    }

    public final void showRewardVideo(String str, boolean z10) {
        Intrinsics.checkNotNullParameter(str, "scenes");
        AdsActivityManager.f33023a.w(str, z10, new Function1(z10, this) { // from class: com.gpower.coloringbynumber.activity.a2

            /* renamed from: n  reason: collision with root package name */
            public final /* synthetic */ boolean f32110n;

            /* renamed from: u  reason: collision with root package name */
            public final /* synthetic */ ColoringActivity f32111u;

            {
                this.f32110n = r1;
                this.f32111u = r2;
            }

            @Override // kotlin.jvm.functions.Function1
            public final Object invoke(Object obj) {
                return ColoringActivity.showRewardVideo$lambda$126(this.f32110n, this.f32111u, ((Integer) obj).intValue());
            }
        }, new Function1(str) { // from class: com.gpower.coloringbynumber.activity.b2

            /* renamed from: u  reason: collision with root package name */
            public final /* synthetic */ String f32118u;

            {
                this.f32118u = r2;
            }

            @Override // kotlin.jvm.functions.Function1
            public final Object invoke(Object obj) {
                return ColoringActivity.showRewardVideo$lambda$127(ColoringActivity.this, this.f32118u, ((Integer) obj).intValue());
            }
        }, new Function1() { // from class: com.gpower.coloringbynumber.activity.c2
            @Override // kotlin.jvm.functions.Function1
            public final Object invoke(Object obj) {
                return ColoringActivity.showRewardVideo$lambda$129(ColoringActivity.this, ((Boolean) obj).booleanValue());
            }
        });
    }

    public final void startVibrator() {
        if (!com.gpower.coloringbynumber.spf.c.f33083b.U0()) {
            return;
        }
        if (Build.VERSION.SDK_INT >= 26) {
            Vibrator vibrator = this.mVibrator;
            if (vibrator != null) {
                q.a(vibrator, p.a(20, -1));
                return;
            }
            return;
        }
        Vibrator vibrator2 = this.mVibrator;
        if (vibrator2 != null) {
            vibrator2.vibrate(20);
        }
    }

    private final void tdEventOutEdit() {
        long j10 = this.openEditTimeDuration;
        if (j10 != 0) {
            TDEventUtils.f33166a.n(fEgNBjrWsgC.bbxzSCdqhK, k2.e.f96840y, String.valueOf(((float) j10) / 1000.0f));
        }
    }

    @Override // android.view.View.OnClickListener
    public void onClick(View view) {
        Integer num;
        LottieAnimationView lottieAnimationView;
        LottieAnimationView lottieAnimationView2;
        ConstraintLayout constraintLayout;
        ConstraintLayout constraintLayout2;
        ConstraintLayout constraintLayout3;
        ConstraintLayout constraintLayout4;
        ConstraintLayout constraintLayout5;
        ConstraintLayout constraintLayout6;
        Object obj;
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding;
        CustomPreviewImageView customPreviewImageView;
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding2;
        ConstraintLayout constraintLayout7;
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding3;
        ConstraintLayout constraintLayout8;
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding4;
        ClipImageView clipImageView;
        LayoutPreviewLayoutBinding layoutPreviewLayoutBinding5;
        ConstraintLayout constraintLayout9;
        IncludeNetworkErrorViewBinding includeNetworkErrorViewBinding;
        ConstraintLayout constraintLayout10;
        Object obj2;
        ViewStubFinishViewBinding viewStubFinishViewBinding;
        PlayerView playerView;
        V25BeanResourceContentsDBM v25BeanResourceContentsDBM = null;
        if (view != null) {
            num = Integer.valueOf(view.getId());
        } else {
            num = null;
        }
        int i10 = R.id.editPathFinishContinueTv;
        boolean z10 = false;
        if (num != null && num.intValue() == i10) {
            try {
                Result.a aVar = Result.Companion;
            } catch (Throwable th) {
                Result.a aVar2 = Result.Companion;
                obj2 = Result.m4962constructorimpl(s0.a(th));
            }
            if (!com.gpower.coloringbynumber.tools.k.f33235a.a(1000)) {
                this.clickExit = true;
                PopToast popToast = this.mPopToast;
                if (popToast != null) {
                    popToast.dismiss();
                }
                this.mPopToast = null;
                this.mAdvPosition = k2.g.R;
                if (!(this.mPlayer == null || (viewStubFinishViewBinding = this.mFinishResultBinding) == null || (playerView = viewStubFinishViewBinding.editPathPlayerView) == null)) {
                    playerView.setVisibility(8);
                }
                com.gpower.coloringbynumber.spf.c cVar = com.gpower.coloringbynumber.spf.c.f33083b;
                if (cVar.z0()) {
                    cVar.e2(cVar.v0() + 1);
                    h0.c cVar2 = h0.c.f88264a;
                    if (cVar.v0() >= 2) {
                        z10 = true;
                    }
                    cVar2.y(z10);
                }
                if (h0.c.f88264a.e()) {
                    showInterstitialAdvAndTD(new Function1() { // from class: com.gpower.coloringbynumber.activity.k1
                        @Override // kotlin.jvm.functions.Function1
                        public final Object invoke(Object obj3) {
                            return ColoringActivity.onClick$lambda$85$lambda$78(ColoringActivity.this, ((Boolean) obj3).booleanValue());
                        }
                    });
                } else {
                    dispatchCurProgressPaintBitmap(new Function1() { // from class: com.gpower.coloringbynumber.activity.l1
                        @Override // kotlin.jvm.functions.Function1
                        public final Object invoke(Object obj3) {
                            return ColoringActivity.onClick$lambda$85$lambda$84(ColoringActivity.this, (Bitmap) obj3);
                        }
                    });
                }
                obj2 = Result.m4962constructorimpl(Unit.f97091a);
                Throwable r02 = Result.m4965exceptionOrNullimpl(obj2);
                if (r02 != null) {
                    com.base.module.tools.model_base.tools.p.a(o0.c.f99366f, "coloringActivity onClick Continue failure : " + r02.getMessage());
                    return;
                }
                return;
            }
            return;
        }
        int i11 = R.id.btn_try_again;
        if (num != null && num.intValue() == i11) {
            ActivityEditBinding activityEditBinding = this.mBinding;
            if (!(activityEditBinding == null || (includeNetworkErrorViewBinding = activityEditBinding.editIncludeErrorLayout) == null || (constraintLayout10 = includeNetworkErrorViewBinding.errorView) == null)) {
                constraintLayout10.setVisibility(8);
            }
            ActivityEditBinding activityEditBinding2 = this.mBinding;
            if (!(activityEditBinding2 == null || (layoutPreviewLayoutBinding5 = activityEditBinding2.editIncludePreviewLayout) == null || (constraintLayout9 = layoutPreviewLayoutBinding5.previewRootLayout) == null)) {
                constraintLayout9.setVisibility(0);
            }
            String mDataId = getMDataId();
            if (mDataId != null) {
                dispatchLoadTemplateInfo(mDataId, getMV25Data());
                return;
            }
            return;
        }
        int i12 = R.id.id_back;
        if (num == null || num.intValue() != i12) {
            int i13 = R.id.editPathWaterMaskIcon;
            if (num == null || num.intValue() != i13) {
                int i14 = R.id.ivHintIcon;
                if (num == null || num.intValue() != i14) {
                    int i15 = R.id.ivAutoImg;
                    if (num == null || num.intValue() != i15) {
                        int i16 = R.id.ivRemoveAdv;
                        if (num != null && num.intValue() == i16) {
                            AllGoodsActivity.Companion.a(this, "editpage");
                            return;
                        }
                        int i17 = R.id.btnScale;
                        if (num != null && num.intValue() == i17) {
                            dispatchScaleZoomDefault(new Function0() { // from class: com.gpower.coloringbynumber.activity.o1
                                @Override // kotlin.jvm.functions.Function0
                                public final Object invoke() {
                                    return ColoringActivity.onClick$lambda$101();
                                }
                            });
                            return;
                        }
                        int i18 = R.id.editPathFinishSkipIcon;
                        if (num != null && num.intValue() == i18) {
                            dispatchPaintedAnimEnd();
                            return;
                        }
                        int i19 = R.id.editPathFinishSaveIcon;
                        if (num != null && num.intValue() == i19) {
                            eventReportSaveOrShare();
                            eventReportClickSaveOrShare(BrjfCofrP.vNvUngBN);
                            this.mSaveRunnable = new Runnable() { // from class: com.gpower.coloringbynumber.activity.p1
                                @Override // java.lang.Runnable
                                public final void run() {
                                    ColoringActivity.onClick$lambda$104(ColoringActivity.this);
                                }
                            };
                            checkUserPermission(new Function0() { // from class: com.gpower.coloringbynumber.activity.q1
                                @Override // kotlin.jvm.functions.Function0
                                public final Object invoke() {
                                    return ColoringActivity.onClick$lambda$105(ColoringActivity.this);
                                }
                            });
                            return;
                        }
                        int i20 = R.id.editPathFinishLikeIcon;
                        if (num != null && num.intValue() == i20) {
                            ViewStubFinishViewBinding viewStubFinishViewBinding2 = this.mFinishResultBinding;
                            if (viewStubFinishViewBinding2 == null || (lottieAnimationView2 = viewStubFinishViewBinding2.lottieLike) == null || !lottieAnimationView2.isAnimating()) {
                                this.isLike = !this.isLike;
                                ViewStubFinishViewBinding viewStubFinishViewBinding3 = this.mFinishResultBinding;
                                if (!(viewStubFinishViewBinding3 == null || (lottieAnimationView = viewStubFinishViewBinding3.lottieLike) == null)) {
                                    lottieAnimationView.playAnimation();
                                }
                                String mDataId2 = getMDataId();
                                if (mDataId2 != null) {
                                    LikeViewModel mLikeViewModel = getMLikeViewModel();
                                    BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo = this.mRelationBean;
                                    if (beanResourceRelationTemplateInfo != null) {
                                        v25BeanResourceContentsDBM = beanResourceRelationTemplateInfo.v25BeanResourceContents();
                                    }
                                    mLikeViewModel.likeRes(mDataId2, v25BeanResourceContentsDBM);
                                    return;
                                }
                                return;
                            }
                            return;
                        }
                        int i21 = R.id.testAutoPaint;
                        if (num != null && num.intValue() == i21) {
                            dispatchOneClickColoring();
                            return;
                        }
                        int i22 = R.id.testAutoPaint2;
                        if (num != null && num.intValue() == i22) {
                            dispatchAutoPaint2();
                            return;
                        }
                        int i23 = R.id.testAllBlockNumber;
                        if (num != null && num.intValue() == i23) {
                            ViewModelAchievement i24 = App.I.b().i();
                            if (i24 != null) {
                                i24.achievementTypeBlockSize(this.blockNumber);
                                return;
                            }
                            return;
                        }
                        int i25 = R.id.editFinishAnimLayout;
                        if (num != null && num.intValue() == i25) {
                            changeAnimStatus();
                            return;
                        }
                        int i26 = R.id.editPathFinishShareIcon;
                        if (num != null && num.intValue() == i26) {
                            eventReportSaveOrShare();
                            eventReportClickSaveOrShare("share");
                            this.mShareRunnable = new Runnable() { // from class: com.gpower.coloringbynumber.activity.s1
                                @Override // java.lang.Runnable
                                public final void run() {
                                    ColoringActivity.onClick$lambda$109(ColoringActivity.this);
                                }
                            };
                            checkUserPermission(new Function0() { // from class: com.gpower.coloringbynumber.activity.t1
                                @Override // kotlin.jvm.functions.Function0
                                public final Object invoke() {
                                    return ColoringActivity.onClick$lambda$110(ColoringActivity.this);
                                }
                            });
                        }
                    } else if (!this.isFinished && !com.gpower.coloringbynumber.tools.k.b(com.gpower.coloringbynumber.tools.k.f33235a, 0, 1, null)) {
                        TDEventUtils.f33166a.c("hint_2", new Object[0]);
                        if (getMColorListAdapter().getSelectPosition() == -1) {
                            BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo2 = this.mRelationBean;
                            if (beanResourceRelationTemplateInfo2 == null || !beanResourceRelationTemplateInfo2.getShowGuessTips()) {
                                ViewStubPaintToolsBinding viewStubPaintToolsBinding = this.mPaintToolsBinding;
                                if (viewStubPaintToolsBinding != null && (constraintLayout = viewStubPaintToolsBinding.activityEditPathBottomLayout) != null) {
                                    getMPopupSelectColor().a(constraintLayout);
                                    this.mHandler.sendEmptyMessageDelayed(311, 3000);
                                    return;
                                }
                                return;
                            }
                            LayoutGuessColoringTipsBinding layoutGuessColoringTipsBinding = this.mVsGuessTipsBinding;
                            if (layoutGuessColoringTipsBinding == null || (constraintLayout3 = layoutGuessColoringTipsBinding.clGuessTips) == null || constraintLayout3.getVisibility() != 0) {
                                this.mHandler.sendEmptyMessageDelayed(163, 3000);
                                LayoutGuessColoringTipsBinding layoutGuessColoringTipsBinding2 = this.mVsGuessTipsBinding;
                                if (layoutGuessColoringTipsBinding2 != null && (constraintLayout2 = layoutGuessColoringTipsBinding2.clGuessTips) != null) {
                                    q0.a(constraintLayout2, true);
                                }
                            }
                        } else if (com.gpower.coloringbynumber.spf.c.f33083b.W0()) {
                            dispatchAutoPaint(this.autoPaintTime);
                        } else if (getMAutoPaintFindNum() > 0) {
                            setMAutoPaintFindNum(getMAutoPaintFindNum() - 1);
                            dispatchAutoPaint(this.autoPaintTime);
                        } else if (getMAutoPaintFindNum() == 0 && AdsActivityManager.f33023a.r()) {
                            this.mRewardCategory = RewardCategory.EDIT_COLOR_AUTO_TOP;
                            showRewardVideo$default(this, "hint_2", false, 2, null);
                        }
                    }
                } else if (!this.isFinished && !com.gpower.coloringbynumber.tools.k.b(com.gpower.coloringbynumber.tools.k.f33235a, 0, 1, null)) {
                    propGiftRestartCalculationTime(4);
                    if (getMColorListAdapter().getSelectPosition() == -1) {
                        BeanResourceRelationTemplateInfo beanResourceRelationTemplateInfo3 = this.mRelationBean;
                        if (beanResourceRelationTemplateInfo3 == null || !beanResourceRelationTemplateInfo3.getShowGuessTips()) {
                            ViewStubPaintToolsBinding viewStubPaintToolsBinding2 = this.mPaintToolsBinding;
                            if (viewStubPaintToolsBinding2 != null && (constraintLayout4 = viewStubPaintToolsBinding2.activityEditPathBottomLayout) != null) {
                                getMPopupSelectColor().a(constraintLayout4);
                                this.mHandler.sendEmptyMessageDelayed(311, 3000);
                                return;
                            }
                            return;
                        }
                        LayoutGuessColoringTipsBinding layoutGuessColoringTipsBinding3 = this.mVsGuessTipsBinding;
                        if (layoutGuessColoringTipsBinding3 == null || (constraintLayout6 = layoutGuessColoringTipsBinding3.clGuessTips) == null || constraintLayout6.getVisibility() != 0) {
                            this.mHandler.sendEmptyMessageDelayed(163, 3000);
                            LayoutGuessColoringTipsBinding layoutGuessColoringTipsBinding4 = this.mVsGuessTipsBinding;
                            if (layoutGuessColoringTipsBinding4 != null && (constraintLayout5 = layoutGuessColoringTipsBinding4.clGuessTips) != null) {
                                q0.a(constraintLayout5, true);
                            }
                        }
                    } else if (this.isBoughtCollectPackage || com.gpower.coloringbynumber.spf.c.f33083b.W0()) {
                        ViewModelAchievement i27 = App.I.b().i();
                        if (i27 != null) {
                            i27.achievementTypeAddProgress(7);
                        }
                        eventReportUseProp();
                        dispatchZoomCurSelectArea$default(this, 0, null, 3, null);
                    } else if (Intrinsics.g(this.abTestTipProp, o0.a.f99347a.b()) && this.propsUnUse) {
                        String string = getString(R.string.prop_un_use);
                        Intrinsics.checkNotNullExpressionValue(string, "getString(...)");
                        new PopToast(this, string, 0, 4, null).showAtLocation(getWindow().getDecorView(), 48, 0, com.base.module.tools.model_base.tools.m.b(100.0f));
                        dispatchZoomCurSelectArea$default(this, 0, null, 3, null);
                    } else if (getMColoringPropsFindNum() > 0) {
                        ViewModelAchievement i28 = App.I.b().i();
                        if (i28 != null) {
                            i28.achievementTypeAddProgress(7);
                        }
                        setMColoringPropsFindNum(getMColoringPropsFindNum() - 1);
                        this.propsUnUse = true;
                        eventReportUseProp();
                        dispatchZoomCurSelectArea$default(this, 0, null, 3, null);
                    } else {
                        this.mIsShowRemind = false;
                        if (getMColoringPropsFindNum() == 0 && AdsActivityManager.f33023a.r()) {
                            this.mRewardCategory = RewardCategory.EDIT_COLOR_HINT_TOP;
                            showRewardVideo$default(this, k2.g.f96925y, false, 2, null);
                        }
                    }
                }
            } else if (!Intrinsics.g(com.gpower.coloringbynumber.spf.c.f33083b.r(), o0.a.f99347a.b())) {
                showRemoveWaterMarkActivity();
            } else if (!getMDialogRemoveWaterMark().isShowing()) {
                getMDialogRemoveWaterMark().show();
            }
        } else if (!com.gpower.coloringbynumber.tools.k.f33235a.a(1000)) {
            this.clickExit = true;
            try {
                Result.a aVar3 = Result.Companion;
                this.mAdvPosition = k2.g.Q;
                int i29 = this.curPaintStage;
                if (i29 == 1) {
                    f.a aVar4 = this.mFrameAnim;
                    if (aVar4 != null) {
                        if (aVar4 != null) {
                            aVar4.j();
                        }
                        ActivityEditBinding activityEditBinding3 = this.mBinding;
                        if (!(activityEditBinding3 == null || (layoutPreviewLayoutBinding4 = activityEditBinding3.editIncludePreviewLayout) == null || (clipImageView = layoutPreviewLayoutBinding4.previewClipImageView) == null)) {
                            clipImageView.setVisibility(8);
                        }
                    }
                    AnimatorSet animatorSet = this.mAnimSet;
                    if (animatorSet != null) {
                        if (animatorSet != null) {
                            animatorSet.cancel();
                        }
                        ActivityEditBinding activityEditBinding4 = this.mBinding;
                        if (!(activityEditBinding4 == null || (layoutPreviewLayoutBinding3 = activityEditBinding4.editIncludePreviewLayout) == null || (constraintLayout8 = layoutPreviewLayoutBinding3.previewRootLayout) == null)) {
                            constraintLayout8.setAlpha(1.0f);
                        }
                        ActivityEditBinding activityEditBinding5 = this.mBinding;
                        if (!(activityEditBinding5 == null || (layoutPreviewLayoutBinding2 = activityEditBinding5.editIncludePreviewLayout) == null || (constraintLayout7 = layoutPreviewLayoutBinding2.previewRootLayout) == null)) {
                            constraintLayout7.setVisibility(0);
                        }
                        ActivityEditBinding activityEditBinding6 = this.mBinding;
                        if (!(activityEditBinding6 == null || (layoutPreviewLayoutBinding = activityEditBinding6.editIncludePreviewLayout) == null || (customPreviewImageView = layoutPreviewLayoutBinding.previewImage) == null)) {
                            customPreviewImageView.setVisibility(0);
                        }
                    }
                    y1 unused = kotlinx.coroutines.j.f(LifecycleOwnerKt.getLifecycleScope(this), z0.c(), null, new ColoringActivity$onClick$4$1(this, null), 2, null);
                } else if (i29 == 2) {
                    dispatchSaveDrawInfoToDB(2);
                    if (h0.c.f88264a.e()) {
                        showInterstitialAdvAndTD(new Function1() { // from class: com.gpower.coloringbynumber.activity.m1
                            @Override // kotlin.jvm.functions.Function1
                            public final Object invoke(Object obj3) {
                                return ColoringActivity.onClick$lambda$97$lambda$88(ColoringActivity.this, ((Boolean) obj3).booleanValue());
                            }
                        });
                    } else {
                        dispatchScaleZoomDefault(new Function0() { // from class: com.gpower.coloringbynumber.activity.n1
                            @Override // kotlin.jvm.functions.Function0
                            public final Object invoke() {
                                return ColoringActivity.onClick$lambda$97$lambda$96(ColoringActivity.this);
                            }
                        });
                    }
                }
                propGiftCloseAndRemoveHandler();
                obj = Result.m4962constructorimpl(Unit.f97091a);
            } catch (Throwable th2) {
                Result.a aVar5 = Result.Companion;
                obj = Result.m4962constructorimpl(s0.a(th2));
            }
            Throwable r03 = Result.m4965exceptionOrNullimpl(obj);
            if (r03 != null) {
                com.base.module.tools.model_base.tools.p.a(o0.c.f99366f, "coloringActivity onClick id_back failure : " + r03.getMessage());
            }
        }
    }
}