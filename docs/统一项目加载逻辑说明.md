# 统一项目加载逻辑说明

## 设计理念

你说得非常对！Library和Gallery的项目点击逻辑理论上应该是一样的，只是数据来源不同：
- **Library**：按类型加载数据列表（新项目、分类项目等）
- **Gallery**：过滤有进度的项目数据列表

## 统一加载架构

### 核心方法：`loadProjectWithSmartProgressDetection()`

```kotlin
private fun loadProjectWithSmartProgressDetection(
    projectId: String, 
    projectName: String?, 
    projectSource: String?
)
```

### 智能加载流程

```
1. 获取统一项目标识符
   ↓
2. 智能检测是否有保存的进度
   ↓
3. 加载项目数据
   ↓
4. 根据进度情况选择设置方式
   ├─ 有进度 → setupProjectFast()
   └─ 无进度 → setupProject()
```

## 实现细节

### 1. 统一入口处理

**EnhancedMainActivity**：
```kotlin
// 简化的Intent参数传递
val intent = Intent(this, RefactoredSimpleMainActivity::class.java)
intent.putExtra("project_id", project.id)
intent.putExtra("project_name", project.displayName ?: project.id)
intent.putExtra("project_source", project.resourceSource.name)
```

**RefactoredSimpleMainActivity**：
```kotlin
// 统一的参数处理
private fun handleIntentAndLoadProject() {
    val projectId = intent.getStringExtra("project_id")
    val projectName = intent.getStringExtra("project_name")
    val projectSource = intent.getStringExtra("project_source")
    
    when {
        projectId != null -> loadProjectWithSmartProgressDetection(projectId, projectName, projectSource)
        projectName != null -> loadProjectWithSmartProgressDetection(projectName, projectName, projectSource)
        else -> loadFirstValidatedProject()
    }
}
```

### 2. 智能进度检测

```kotlin
// 步骤1：获取统一的项目标识符
val unifiedProjectId = ProjectNameUtils.getUnifiedProjectId(
    intent = intent,
    projectId = projectId,
    projectName = projectName
)

// 步骤2：智能检测是否有保存的进度
val progressResult = progressSaveManager.loadSavedProgress(unifiedProjectId)
val hasProgress = progressResult is LoadResult.Success
```

### 3. 自适应设置方式

```kotlin
if (hasProgress) {
    // 有进度：使用快速设置
    val fullProgressData = (progressResult as LoadResult.Success).data
    setupProjectFast(fullProgressData.coloringData, projectResult.outlineBitmap, fullProgressData.filledRegions)
} else {
    // 无进度：使用标准设置
    setupProject(projectResult.coloringData, projectResult.outlineBitmap)
}
```

## 兼容性保持

所有原有的加载方法都保留并重定向到统一逻辑：

```kotlin
// 兼容性方法
private fun loadProjectById(projectId: String, projectSource: String?) {
    loadProjectWithSmartProgressDetection(projectId, null, projectSource)
}

private fun loadProjectByName(projectName: String) {
    loadProjectWithSmartProgressDetection(projectName, projectName, "BUILT_IN")
}

private fun loadProjectFromGallery(projectName: String, hasProgress: Boolean) {
    loadProjectWithSmartProgressDetection(projectName, projectName, "BUILT_IN")
}
```

## 优势

### 1. 统一性
- Library和Gallery使用完全相同的加载逻辑
- 消除了不同入口的差异化处理

### 2. 智能化
- 自动检测项目是否有进度
- 根据进度情况选择最优的设置方式

### 3. 简化性
- 移除了复杂的`from_gallery`、`has_progress`等标记
- 减少了Intent参数的复杂性

### 4. 可维护性
- 单一的加载逻辑，易于维护和调试
- 统一的错误处理和日志记录

## 测试验证

### 测试场景1：Library新项目
1. 从Library选择新项目
2. **期望**：使用`setupProject()`标准设置
3. **验证**：颜色面板有默认选中色，马赛克可见

### 测试场景2：Library已有进度项目
1. 从Library选择之前填色过的项目
2. **期望**：自动检测到进度，使用`setupProjectFast()`
3. **验证**：正确恢复填色进度

### 测试场景3：Gallery项目
1. 从Gallery选择任意项目
2. **期望**：与Library相同的智能加载逻辑
3. **验证**：进度恢复与Library一致

### 测试场景4：项目名称一致性
1. 测试不同来源的相同项目
2. **期望**：使用统一的项目标识符
3. **验证**：进度保存和加载一致

## 日志关键词

搜索以下关键词来验证统一逻辑：
- `统一项目加载逻辑`
- `统一项目标识符`
- `进度检测结果`
- `使用快速设置恢复进度`
- `使用标准设置（无进度）`

## 总结

这个统一的加载逻辑实现了：
- ✅ Library和Gallery使用相同的项目加载机制
- ✅ 智能的进度检测和恢复
- ✅ 简化的Intent参数传递
- ✅ 统一的项目标识符处理
- ✅ 自适应的项目设置方式

这样的设计确保了无论从哪个入口进入，用户都能获得一致的体验。